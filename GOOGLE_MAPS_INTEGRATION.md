# تكامل خرائط Google لتطبيق باصاتي

## نظرة عامة
يصف هذا المستند تكامل خرائط Google المُطبق في تطبيق باصاتي، خاصة لعرض مسارات الرحلات ومواقع حضور الطلاب في تفاصيل الرحلات السابقة.

## Features Implemented

### 1. Trip Route Display
- **Real-time route visualization** using Google Maps
- **Route polylines** connecting all trip points
- **Custom markers** for start, intermediate, and end points
- **Interactive info windows** showing time stamps for each point

### 2. Student Location Markers
- **Present students** marked with green markers (✅)
- **Absent students** marked with orange markers (❌)
- **Clustered markers** for students at the same location
- **Detailed info windows** with student information

### 3. Interactive Map Features
- **Auto-fit bounds** to show all route points and student locations
- **Zoom controls** and map navigation
- **Legend** showing marker meanings
- **Statistics panel** with route and attendance data

## Files Added/Modified

### New Files
1. **`lib/presentation/widgets/trips/trip_route_map.dart`**
   - Main Google Maps widget for trip route display
   - Handles route polylines and markers
   - Integrates with student attendance data

2. **`lib/presentation/widgets/trips/student_map_markers.dart`**
   - Utility class for creating student markers
   - Handles marker clustering and info windows
   - Provides statistics for student locations

3. **`lib/data/models/student_attendance_model.dart`**
   - Data model for student attendance information
   - Includes location data (latitude/longitude)
   - Supports present/absent status tracking

4. **`lib/data/models/route_model.dart`**
   - Data models for trip route information
   - RoutePointModel for individual GPS points
   - TripRouteModel for complete route data

5. **`lib/presentation/controllers/previous_trip_details_controller.dart`**
   - Controller managing trip details state
   - Handles loading of attendance and route data
   - Provides computed properties for UI

6. **`lib/presentation/pages/trips/previous_trip_details_page.dart`**
   - Main page for previous trip details
   - Three-tab interface: Route Map, Present Students, Absent Students
   - Responsive design for mobile and desktop

7. **`lib/presentation/widgets/trips/trip_details_header.dart`**
   - Header widget showing trip information
   - Statistics display for attendance
   - Status indicators

8. **`lib/presentation/widgets/trips/attendance_tab.dart`**
   - Tab for displaying student attendance lists
   - Separate views for present and absent students
   - Pull-to-refresh functionality

9. **`lib/presentation/widgets/trips/route_map_tab.dart`**
   - Tab containing the Google Maps route display
   - Route statistics and information
   - Integration with TripRouteMap widget

### Modified Files
1. **`pubspec.yaml`**
   - Added Google Maps dependencies:
     - `google_maps_flutter: ^2.5.3`
     - `google_maps_flutter_web: ^0.5.4+3`

2. **`web/index.html`**
   - Added Google Maps JavaScript API script
   - Placeholder for API key configuration

3. **`lib/data/datasources/trip_remote_data_source.dart`**
   - Added methods for fetching trip attendance data
   - Added method for fetching trip route details
   - API endpoints for absent students and route data

4. **`lib/presentation/routes/app_pages.dart`**
   - Added route for previous trip details page
   - Updated imports for new pages

5. **`lib/presentation/pages/trips/previous_trips_page.dart`**
   - Updated navigation to pass trip details
   - Enhanced data passing to details page

6. **`lib/core/utils/responsive_utils.dart`**
   - Added getFontSize helper method
   - Improved responsive design utilities

## API Endpoints Used

### Trip Attendance
- **Present Students**: `general/trips/attendants/{tripId}`
- **Absent Students**: `general/trips/absents/{tripId}`

### Trip Route
- **Route Details**: `general/trips/routes/{tripId}`

## Setup Instructions

### 1. Google Maps API Key
1. Get a Google Maps API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the following APIs:
   - Maps JavaScript API
   - Maps SDK for Android (if building for Android)
   - Maps SDK for iOS (if building for iOS)

### 2. Configuration
1. **Web**: Replace `YOUR_API_KEY_HERE` in `web/index.html` with your actual API key
2. **Android**: Add API key to `android/app/src/main/AndroidManifest.xml`
3. **iOS**: Add API key to `ios/Runner/AppDelegate.swift`

### 3. Dependencies
Run the following command to install dependencies:
```bash
flutter pub get
```

## Usage

### Viewing Previous Trip Details
1. Navigate to "Previous Trips" page
2. Click on any trip to view details
3. Use the three tabs to view:
   - **Route Map**: Interactive map with route and student locations
   - **Present Students**: List of students who attended
   - **Absent Students**: List of students who were absent

### Map Features
- **Zoom**: Use pinch gestures or zoom controls
- **Pan**: Drag to move around the map
- **Markers**: Tap markers to see detailed information
- **Auto-fit**: Map automatically adjusts to show all relevant points

## Data Models

### StudentAttendanceModel
```dart
class StudentAttendanceModel {
  final int? id;
  final String? name;
  final bool? isPresent;
  final String? attendanceTime;
  final String? latitude;
  final String? longitude;
  // ... other fields
}
```

### RoutePointModel
```dart
class RoutePointModel {
  final int? id;
  final int? tripId;
  final String? latitude;
  final String? longitude;
  final String? type;
  final String? createdAt;
}
```

### TripRouteModel
```dart
class TripRouteModel {
  final int? tripId;
  final String? startTime;
  final String? endTime;
  final double? totalDistance;
  final int? estimatedTime;
  final List<RoutePointModel>? routePoints;
}
```

## Future Enhancements

### Planned Features
1. **Real-time tracking** for current trips
2. **Custom marker icons** with student photos
3. **Route optimization** suggestions
4. **Geofencing** for pickup/drop-off points
5. **Offline map support** for areas with poor connectivity
6. **Export functionality** for route data
7. **Integration with navigation apps**

### Performance Optimizations
1. **Marker clustering** for large numbers of students
2. **Lazy loading** of map data
3. **Caching** of route information
4. **Progressive loading** of student details

## Troubleshooting

### Common Issues
1. **Map not loading**: Check API key configuration
2. **Markers not appearing**: Verify coordinate data format
3. **Performance issues**: Consider implementing marker clustering
4. **Network errors**: Check API endpoint availability

### Debug Tips
1. Enable debug mode in Google Maps
2. Check browser console for JavaScript errors
3. Verify API key permissions and quotas
4. Test with sample coordinate data

## Security Considerations

### API Key Protection
1. **Restrict API key** to specific domains/apps
2. **Set usage quotas** to prevent abuse
3. **Monitor usage** through Google Cloud Console
4. **Use environment variables** for key storage

### Data Privacy
1. **Student location data** should be handled securely
2. **Implement proper access controls**
3. **Follow data retention policies**
4. **Ensure GDPR compliance** if applicable

## Support

For technical support or questions about the Google Maps integration:
1. Check the Flutter Google Maps plugin documentation
2. Review Google Maps Platform documentation
3. Contact the development team for app-specific issues

---

**Note**: This integration requires a valid Google Maps API key and appropriate billing setup for production use. Development and testing can be done with the free tier quotas.
