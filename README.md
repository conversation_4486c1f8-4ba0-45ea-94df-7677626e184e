# باصاتي - Busaty

تطبيق ويب شامل لإدارة النقل المدرسي مبني بـ Flutter.

## البدء

هذا المشروع هو تطبيق ويب شامل لإدارة النقل المدرسي يوفر:

- إدارة الطلاب والحافلات
- تتبع الرحلات في الوقت الفعلي
- نظام الحضور والغياب
- إدارة السائقين والمشرفين
- تقارير مفصلة

## الميزات الرئيسية

- **واجهة مستخدم حديثة**: مبنية بـ Flutter مع دعم كامل للغة العربية
- **تتبع الموقع**: تكامل مع خرائط Google لتتبع الحافلات
- **إدارة شاملة**: نظام متكامل لإدارة جميع جوانب النقل المدرسي
- **تقارير متقدمة**: تقارير تفصيلية عن الحضور والرحلات

## متطلبات النظام

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
