import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/parent.dart';

/// Parent repository interface
/// Following Clean Architecture principles and matching SchoolX structure
abstract class ParentRepository {
  /// Get all parents with pagination
  Future<Either<Failure, List<Parent>>> getParents({
    int page = 1,
    int limit = 10,
    String? search,
  });

  /// Get parent by ID
  Future<Either<Failure, Parent>> getParentById(int id);

  /// Create new parent
  Future<Either<Failure, Parent>> createParent(Parent parent);

  /// Update existing parent
  Future<Either<Failure, Parent>> updateParent(Parent parent);

  /// Delete parent
  Future<Either<Failure, bool>> deleteParent(int id);

  /// Get students by parent ID
  Future<Either<Failure, List<int>>> getStudentsByParentId(int parentId);

  /// Get available schools for parent assignment
  Future<Either<Failure, List<Map<String, dynamic>>>> getAvailableSchools();

  /// Get gender options for parent form
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenderOptions();

  /// Get religion options for parent form
  Future<Either<Failure, List<Map<String, dynamic>>>> getReligionOptions();
}
