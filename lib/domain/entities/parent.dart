import 'package:equatable/equatable.dart';

/// Parent entity representing a parent in the domain layer
/// Following Clean Architecture principles and matching SchoolX structure
class Parent extends Equatable {
  final int? id;
  final String? email;
  final String? username;
  final String? name;
  final int? genderId;
  final int? schoolId;
  final int? religionId;
  final String? joiningDate;
  final String? address;
  final String? cityName;
  final int? status;
  final String? logo;
  final String? type;
  final String? phone;
  final String? birthDate;
  final String? nationalId;
  final String? job;
  final String? emailVerifiedAt;
  final String? deletedAt;
  final String? createdAt;
  final String? updatedAt;
  final String? typeAuth;
  final String? logoPath;
  final String? schoolName;
  final String? genderName;
  final String? religionName;
  final List<int>? studentIds;
  final String? parentKey;
  final String? parentSecret;

  const Parent({
    this.id,
    this.email,
    this.username,
    this.name,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.joiningDate,
    this.address,
    this.cityName,
    this.status,
    this.logo,
    this.type,
    this.phone,
    this.birthDate,
    this.nationalId,
    this.job,
    this.emailVerifiedAt,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.typeAuth,
    this.logoPath,
    this.schoolName,
    this.genderName,
    this.religionName,
    this.studentIds,
    this.parentKey,
    this.parentSecret,
  });

  @override
  List<Object?> get props => [
    id,
    email,
    username,
    name,
    genderId,
    schoolId,
    religionId,
    joiningDate,
    address,
    cityName,
    status,
    logo,
    type,
    phone,
    birthDate,
    nationalId,
    job,
    emailVerifiedAt,
    deletedAt,
    createdAt,
    updatedAt,
    typeAuth,
    logoPath,
    schoolName,
    genderName,
    religionName,
    studentIds,
    parentKey,
    parentSecret,
  ];

  /// Create a copy of this parent with updated fields
  Parent copyWith({
    int? id,
    String? email,
    String? username,
    String? name,
    int? genderId,
    int? schoolId,
    int? religionId,
    String? joiningDate,
    String? address,
    String? cityName,
    int? status,
    String? logo,
    String? type,
    String? phone,
    String? birthDate,
    String? nationalId,
    String? job,
    String? emailVerifiedAt,
    String? deletedAt,
    String? createdAt,
    String? updatedAt,
    String? typeAuth,
    String? logoPath,
    String? schoolName,
    String? genderName,
    String? religionName,
    List<int>? studentIds,
    String? parentKey,
    String? parentSecret,
  }) {
    return Parent(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      name: name ?? this.name,
      genderId: genderId ?? this.genderId,
      schoolId: schoolId ?? this.schoolId,
      religionId: religionId ?? this.religionId,
      joiningDate: joiningDate ?? this.joiningDate,
      address: address ?? this.address,
      cityName: cityName ?? this.cityName,
      status: status ?? this.status,
      logo: logo ?? this.logo,
      type: type ?? this.type,
      phone: phone ?? this.phone,
      birthDate: birthDate ?? this.birthDate,
      nationalId: nationalId ?? this.nationalId,
      job: job ?? this.job,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      typeAuth: typeAuth ?? this.typeAuth,
      logoPath: logoPath ?? this.logoPath,
      schoolName: schoolName ?? this.schoolName,
      genderName: genderName ?? this.genderName,
      religionName: religionName ?? this.religionName,
      studentIds: studentIds ?? this.studentIds,
      parentKey: parentKey ?? this.parentKey,
      parentSecret: parentSecret ?? this.parentSecret,
    );
  }

  @override
  String toString() {
    return 'Parent{id: $id, name: $name, email: $email, phone: $phone}';
  }
}
