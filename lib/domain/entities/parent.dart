/// Parent entity representing a parent in the domain layer
/// Following Clean Architecture principles
class Parent {
  final int id;
  final String name;
  final String? email;
  final String? phone;
  final String? address;
  final String? logoPath;
  final List<int>? studentIds;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Parent({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.logoPath,
    this.studentIds,
    this.createdAt,
    this.updatedAt,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Parent &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Parent{id: $id, name: $name, email: $email, phone: $phone}';
  }
}
