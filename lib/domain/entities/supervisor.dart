import 'package:equatable/equatable.dart';

/// Supervisor entity
/// Following Single Responsibility Principle by focusing only on supervisor data
class Supervisor extends Equatable {
  final int? id;
  final String? email;
  final String? username;
  final String? name;
  final int? genderId;
  final int? schoolId;
  final int? religionId;
  final String? joiningDate;
  final String? address;
  final int? busId;
  final String? cityName;
  final int? status;
  final String? logo;
  final String? type;
  final String? phone;
  final String? birthDate;
  final String? emailVerifiedAt;
  final String? deletedAt;
  final String? createdAt;
  final String? updatedAt;
  final String? typeAuth;
  final String? logoPath;
  final String? schoolName;
  final String? genderName;
  final String? religionName;
  final String? busName;
  final String? busCarNumber;
  final String? drivers;

  const Supervisor({
    this.id,
    this.email,
    this.username,
    this.name,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.joiningDate,
    this.address,
    this.busId,
    this.cityName,
    this.status,
    this.logo,
    this.type,
    this.phone,
    this.birthDate,
    this.emailVerifiedAt,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.typeAuth,
    this.logoPath,
    this.schoolName,
    this.genderName,
    this.religionName,
    this.busName,
    this.busCarNumber,
    this.drivers,
  });

  @override
  List<Object?> get props => [
        id,
        email,
        username,
        name,
        genderId,
        schoolId,
        religionId,
        joiningDate,
        address,
        busId,
        cityName,
        status,
        logo,
        type,
        phone,
        birthDate,
        emailVerifiedAt,
        deletedAt,
        createdAt,
        updatedAt,
        typeAuth,
        logoPath,
        schoolName,
        genderName,
        religionName,
        busName,
        busCarNumber,
        drivers,
      ];

  /// Create a copy of this supervisor with updated fields
  Supervisor copyWith({
    int? id,
    String? email,
    String? username,
    String? name,
    int? genderId,
    int? schoolId,
    int? religionId,
    String? joiningDate,
    String? address,
    int? busId,
    String? cityName,
    int? status,
    String? logo,
    String? type,
    String? phone,
    String? birthDate,
    String? emailVerifiedAt,
    String? deletedAt,
    String? createdAt,
    String? updatedAt,
    String? typeAuth,
    String? logoPath,
    String? schoolName,
    String? genderName,
    String? religionName,
    String? busName,
    String? busCarNumber,
    String? drivers,
  }) {
    return Supervisor(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      name: name ?? this.name,
      genderId: genderId ?? this.genderId,
      schoolId: schoolId ?? this.schoolId,
      religionId: religionId ?? this.religionId,
      joiningDate: joiningDate ?? this.joiningDate,
      address: address ?? this.address,
      busId: busId ?? this.busId,
      cityName: cityName ?? this.cityName,
      status: status ?? this.status,
      logo: logo ?? this.logo,
      type: type ?? this.type,
      phone: phone ?? this.phone,
      birthDate: birthDate ?? this.birthDate,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      typeAuth: typeAuth ?? this.typeAuth,
      logoPath: logoPath ?? this.logoPath,
      schoolName: schoolName ?? this.schoolName,
      genderName: genderName ?? this.genderName,
      religionName: religionName ?? this.religionName,
      busName: busName ?? this.busName,
      busCarNumber: busCarNumber ?? this.busCarNumber,
      drivers: drivers ?? this.drivers,
    );
  }
}
