import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../core/usecases/usecase.dart';
import '../repositories/parent_repository.dart';

/// Delete parent use case
/// Following Clean Architecture principles
class DeleteParentUseCase implements UseCase<bool, int> {
  final ParentRepository repository;

  DeleteParentUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(int params) async {
    return await repository.deleteParent(params);
  }
}
