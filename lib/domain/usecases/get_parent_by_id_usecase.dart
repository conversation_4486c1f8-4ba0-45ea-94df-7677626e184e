import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/parent.dart';
import '../repositories/parent_repository.dart';

/// Get parent by ID use case
/// Following Clean Architecture principles
class GetParentByIdUseCase implements UseCase<Parent, int> {
  final ParentRepository repository;

  GetParentByIdUseCase(this.repository);

  @override
  Future<Either<Failure, Parent>> call(int params) async {
    return await repository.getParentById(params);
  }
}
