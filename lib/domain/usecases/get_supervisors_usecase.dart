import 'package:dartz/dartz.dart';
import '../entities/supervisor.dart';
import '../repositories/supervisor_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for getting supervisors
/// Following Single Responsibility Principle by focusing only on getting supervisors
class GetSupervisorsUseCase implements UseCase<List<Supervisor>, GetSupervisorsParams> {
  final SupervisorRepository repository;

  GetSupervisorsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Supervisor>>> call(GetSupervisorsParams params) {
    return repository.getSupervisors(
      page: params.page,
      limit: params.limit,
    );
  }
}

/// Parameters for getting supervisors
class GetSupervisorsParams {
  final int page;
  final int limit;

  GetSupervisorsParams({
    this.page = 1,
    this.limit = 10,
  });
}
