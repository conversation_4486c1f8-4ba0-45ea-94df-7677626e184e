import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/parent.dart';
import '../repositories/parent_repository.dart';

/// Create parent use case
/// Following Clean Architecture principles
class CreateParentUseCase implements UseCase<Parent, Parent> {
  final ParentRepository repository;

  CreateParentUseCase(this.repository);

  @override
  Future<Either<Failure, Parent>> call(Parent params) async {
    return await repository.createParent(params);
  }
}
