import 'package:dartz/dartz.dart';
import '../entities/supervisor.dart';
import '../repositories/supervisor_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for getting supervisor by ID
/// Following Single Responsibility Principle by focusing only on getting supervisor by ID
class GetSupervisorByIdUseCase implements UseCase<Supervisor, int> {
  final SupervisorRepository repository;

  GetSupervisorByIdUseCase(this.repository);

  @override
  Future<Either<Failure, Supervisor>> call(int id) {
    return repository.getSupervisorById(id);
  }
}
