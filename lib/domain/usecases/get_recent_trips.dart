import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/trip.dart';
import '../repositories/trip_repository.dart';

/// Parameters for GetRecentTrips use case
class GetRecentTripsParams {
  final int page;
  final int limit;
  final String? busId;
  final String? date;
  final String? search;

  const GetRecentTripsParams({
    this.page = 1,
    this.limit = 10,
    this.busId,
    this.date,
    this.search,
  });
}

/// GetRecentTripsUseCase use case
/// Following Single Responsibility Principle by focusing only on getting recent trips
class GetRecentTripsUseCase {
  final TripRepository repository;

  GetRecentTripsUseCase(this.repository);

  Future<Either<Failure, List<Trip>>> call({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
  }) async {
    return await repository.getRecentTrips(
      page: page,
      limit: limit,
      busId: busId,
      date: date,
      search: search,
    );
  }
}
