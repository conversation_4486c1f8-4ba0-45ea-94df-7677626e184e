import 'package:dartz/dartz.dart';
import '../../entities/trip.dart';
import '../../repositories/trip_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';

/// End trip use case
/// Following Single Responsibility Principle by focusing only on trip ending
class EndTripUseCase {
  final TripRepository repository;

  EndTripUseCase(this.repository);

  Future<Either<Failure, Trip>> call(String tripId) async {
    try {
      LoggerService.debug('Ending trip', data: {'tripId': tripId});

      if (tripId.trim().isEmpty) {
        return Left(ValidationFailure(message: 'معرف الرحلة مطلوب'));
      }

      final result = await repository.endTrip(tripId);
      
      return result.fold(
        (failure) {
          LoggerService.error('Failed to end trip', error: failure);
          return Left(failure);
        },
        (endedTrip) {
          LoggerService.info('Trip ended successfully', data: {
            'tripId': endedTrip.id,
            'status': endedTrip.status,
            'endTime': endedTrip.endTime?.toIso8601String(),
          });
          return Right(endedTrip);
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error ending trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

/// Validation failure class
class ValidationFailure extends Failure {
  ValidationFailure({required String message}) : super(message: message);
}
