import 'package:dartz/dartz.dart';
import '../../entities/trip.dart';
import '../../repositories/trip_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';

/// Get trips use case with filtering and pagination
/// Following Single Responsibility Principle by focusing only on trip retrieval
class GetTripsUseCase {
  final TripRepository repository;

  GetTripsUseCase(this.repository);

  Future<Either<Failure, List<Trip>>> call({
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? busId,
    String? driverId,
    String? supervisorId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.debug('Getting trips with filters', data: {
        'page': page,
        'limit': limit,
        'search': search,
        'status': status,
        'busId': busId,
        'driverId': driverId,
        'supervisorId': supervisorId,
        'startDate': startDate?.toIso8601String(),
        'endDate': endDate?.toIso8601String(),
      });

      final result = await repository.getTrips(
        page: page,
        limit: limit,
        search: search,
        status: status,
        busId: busId,
        driverId: driverId,
        supervisorId: supervisorId,
        startDate: startDate,
        endDate: endDate,
      );
      
      return result.fold(
        (failure) {
          LoggerService.error('Failed to get trips', error: failure);
          return Left(failure);
        },
        (trips) {
          LoggerService.info('Trips retrieved successfully', data: {
            'count': trips.length,
            'page': page,
          });
          return Right(trips);
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error getting trips', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
