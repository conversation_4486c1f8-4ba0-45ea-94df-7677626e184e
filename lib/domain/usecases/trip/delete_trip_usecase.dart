import 'package:dartz/dartz.dart';
import '../../repositories/trip_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';

/// Delete trip use case
/// Following Single Responsibility Principle by focusing only on trip deletion
class DeleteTripUseCase {
  final TripRepository repository;

  DeleteTripUseCase(this.repository);

  Future<Either<Failure, bool>> call(String tripId) async {
    try {
      LoggerService.debug('Deleting trip', data: {'tripId': tripId});

      if (tripId.trim().isEmpty) {
        return Left(ValidationFailure(message: 'معرف الرحلة مطلوب'));
      }

      final result = await repository.deleteTrip(tripId);
      
      return result.fold(
        (failure) {
          LoggerService.error('Failed to delete trip', error: failure);
          return Left(failure);
        },
        (success) {
          LoggerService.info('Trip deleted successfully', data: {'tripId': tripId});
          return Right(success);
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error deleting trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

/// Validation failure class
class ValidationFailure extends Failure {
  ValidationFailure({required String message}) : super(message: message);
}
