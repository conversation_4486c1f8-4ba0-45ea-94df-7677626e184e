import 'package:dartz/dartz.dart';
import '../../entities/trip.dart';
import '../../repositories/trip_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';

/// Create trip use case
/// Following Single Responsibility Principle by focusing only on trip creation
class CreateTripUseCase {
  final TripRepository repository;

  CreateTripUseCase(this.repository);

  Future<Either<Failure, Trip>> call(Trip trip) async {
    try {
      LoggerService.debug('Creating trip', data: {
        'tripName': trip.name,
        'busId': trip.busId,
        'driverId': trip.driverId,
        'supervisorId': trip.supervisorId,
      });

      // Validate trip data
      final validationResult = _validateTrip(trip);
      if (validationResult != null) {
        return Left(ValidationFailure(message: validationResult));
      }

      final result = await repository.createTrip(trip);
      
      return result.fold(
        (failure) {
          LoggerService.error('Failed to create trip', error: failure);
          return Left(failure);
        },
        (createdTrip) {
          LoggerService.info('Trip created successfully', data: {
            'tripId': createdTrip.id,
            'tripName': createdTrip.name,
          });
          return Right(createdTrip);
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error creating trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  String? _validateTrip(Trip trip) {
    if (trip.name.trim().isEmpty) {
      return 'اسم الرحلة مطلوب';
    }

    if (trip.busId.trim().isEmpty) {
      return 'معرف الباص مطلوب';
    }

    if (trip.driverId.trim().isEmpty) {
      return 'معرف السائق مطلوب';
    }

    if (trip.supervisorId.trim().isEmpty) {
      return 'معرف المشرف مطلوب';
    }

    if (trip.startLocation.trim().isEmpty) {
      return 'موقع البداية مطلوب';
    }

    if (trip.endLocation.trim().isEmpty) {
      return 'موقع النهاية مطلوب';
    }

    if (trip.studentIds.isEmpty) {
      return 'يجب تحديد طلاب للرحلة';
    }

    return null;
  }
}

/// Validation failure class
class ValidationFailure extends Failure {
  ValidationFailure({required String message}) : super(message: message);
}
