import 'package:dartz/dartz.dart';
import '../../entities/trip.dart';
import '../../repositories/trip_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';

/// Update trip use case
/// Following Single Responsibility Principle by focusing only on trip updates
class UpdateTripUseCase {
  final TripRepository repository;

  UpdateTripUseCase(this.repository);

  Future<Either<Failure, Trip>> call(Trip trip) async {
    try {
      LoggerService.debug('Updating trip', data: {
        'tripId': trip.id,
        'tripName': trip.name,
      });

      // Validate trip data
      final validationResult = _validateTrip(trip);
      if (validationResult != null) {
        return Left(ValidationFailure(message: validationResult));
      }

      final result = await repository.updateTrip(trip);
      
      return result.fold(
        (failure) {
          LoggerService.error('Failed to update trip', error: failure);
          return Left(failure);
        },
        (updatedTrip) {
          LoggerService.info('Trip updated successfully', data: {
            'tripId': updatedTrip.id,
            'tripName': updatedTrip.name,
          });
          return Right(updatedTrip);
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error updating trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  String? _validateTrip(Trip trip) {
    if (trip.id.trim().isEmpty) {
      return 'معرف الرحلة مطلوب';
    }

    if (trip.name.trim().isEmpty) {
      return 'اسم الرحلة مطلوب';
    }

    if (trip.busId.trim().isEmpty) {
      return 'معرف الباص مطلوب';
    }

    if (trip.driverId.trim().isEmpty) {
      return 'معرف السائق مطلوب';
    }

    if (trip.supervisorId.trim().isEmpty) {
      return 'معرف المشرف مطلوب';
    }

    if (trip.startLocation.trim().isEmpty) {
      return 'موقع البداية مطلوب';
    }

    if (trip.endLocation.trim().isEmpty) {
      return 'موقع النهاية مطلوب';
    }

    return null;
  }
}

/// Validation failure class
class ValidationFailure extends Failure {
  ValidationFailure({required String message}) : super(message: message);
}
