import 'package:dartz/dartz.dart';
import '../../entities/trip_tracking_data.dart';
import '../../repositories/trip_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';

/// Get trip tracking use case
/// Following Single Responsibility Principle by focusing only on trip tracking data
class GetTripTrackingUseCase {
  final TripRepository repository;

  GetTripTrackingUseCase(this.repository);

  Future<Either<Failure, TripTrackingData>> call(String tripId) async {
    try {
      LoggerService.debug('Getting trip tracking data', data: {'tripId': tripId});

      if (tripId.trim().isEmpty) {
        return Left(ValidationFailure(message: 'معرف الرحلة مطلوب'));
      }

      final result = await repository.getTripTracking(tripId);
      
      return result.fold(
        (failure) {
          LoggerService.error('Failed to get trip tracking data', error: failure);
          return Left(failure);
        },
        (trackingData) {
          LoggerService.info('Trip tracking data retrieved successfully', data: {
            'tripId': trackingData.tripId,
            'status': trackingData.status,
            'currentLatitude': trackingData.currentLatitude,
            'currentLongitude': trackingData.currentLongitude,
          });
          return Right(trackingData);
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error getting trip tracking data', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

/// Validation failure class
class ValidationFailure extends Failure {
  ValidationFailure({required String message}) : super(message: message);
}
