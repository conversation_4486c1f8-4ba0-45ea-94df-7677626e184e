import 'package:dartz/dartz.dart';
import '../repositories/supervisor_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for deleting supervisor
/// Following Single Responsibility Principle by focusing only on deleting supervisor
class DeleteSupervisorUseCase implements UseCase<bool, int> {
  final SupervisorRepository repository;

  DeleteSupervisorUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(int id) {
    return repository.deleteSupervisor(id);
  }
}
