import 'package:equatable/equatable.dart';

/// Student attendance model for trip details
/// Based on the original SchoolX project structure
class StudentAttendanceModel extends Equatable {
  final int? id;
  final String? name;
  final bool? isPresent;
  final String? attendanceTime;
  final String? grade;
  final String? classroom;
  final String? profileImage;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final int? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final String? createdAt;
  final String? updatedAt;
  final String? logoPath;
  final AttendantStudentPivot? pivot;

  const StudentAttendanceModel({
    this.id,
    this.name,
    this.isPresent,
    this.attendanceTime,
    this.grade,
    this.classroom,
    this.profileImage,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.classroomId,
    this.busId,
    this.address,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
    this.pivot,
  });

  factory StudentAttendanceModel.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return StudentAttendanceModel(
      id: parseInt(json['id']),
      name: json['name']?.toString(),
      isPresent: json['is_present'] as bool?,
      attendanceTime: json['attendance_time']?.toString(),
      grade: json['grade']?.toString(),
      classroom: json['classroom']?.toString(),
      profileImage: json['profile_image']?.toString(),
      phone: json['phone']?.toString(),
      gradeId: parseInt(json['grade_id']),
      genderId: parseInt(json['gender_id']),
      schoolId: parseInt(json['school_id']),
      classroomId: parseInt(json['classroom_id']),
      busId: parseInt(json['bus_id']),
      address: json['address']?.toString(),
      status: parseInt(json['status']),
      tripType: json['trip_type']?.toString(),
      parentKey: json['parent_key']?.toString(),
      parentSecret: json['parent_secret']?.toString(),
      logo: json['logo']?.toString(),
      latitude: json['latitude']?.toString(),
      longitude: json['longitude']?.toString(),
      createdAt: json['created_at']?.toString(),
      updatedAt: json['updated_at']?.toString(),
      logoPath: json['logo_path']?.toString(),
      pivot: json['pivot'] != null 
          ? AttendantStudentPivot.fromJson(json['pivot']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'is_present': isPresent,
      'attendance_time': attendanceTime,
      'grade': grade,
      'classroom': classroom,
      'profile_image': profileImage,
      'phone': phone,
      'grade_id': gradeId,
      'gender_id': genderId,
      'school_id': schoolId,
      'classroom_id': classroomId,
      'bus_id': busId,
      'address': address,
      'status': status,
      'trip_type': tripType,
      'parent_key': parentKey,
      'parent_secret': parentSecret,
      'logo': logo,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'logo_path': logoPath,
      'pivot': pivot?.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        isPresent,
        attendanceTime,
        grade,
        classroom,
        profileImage,
        phone,
        gradeId,
        genderId,
        schoolId,
        classroomId,
        busId,
        address,
        status,
        tripType,
        parentKey,
        parentSecret,
        logo,
        latitude,
        longitude,
        createdAt,
        updatedAt,
        logoPath,
        pivot,
      ];
}

/// Attendant student pivot model
class AttendantStudentPivot extends Equatable {
  final int? tripId;
  final int? studentId;
  final String? arrivedAt;
  final String? onboardAt;
  final String? createdAt;
  final String? updatedAt;

  const AttendantStudentPivot({
    this.tripId,
    this.studentId,
    this.arrivedAt,
    this.onboardAt,
    this.createdAt,
    this.updatedAt,
  });

  factory AttendantStudentPivot.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return AttendantStudentPivot(
      tripId: parseInt(json['trip_id']),
      studentId: parseInt(json['student_id']),
      arrivedAt: json['arrived_at']?.toString(),
      onboardAt: json['onboard_at']?.toString(),
      createdAt: json['created_at']?.toString(),
      updatedAt: json['updated_at']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trip_id': tripId,
      'student_id': studentId,
      'arrived_at': arrivedAt,
      'onboard_at': onboardAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  List<Object?> get props => [
        tripId,
        studentId,
        arrivedAt,
        onboardAt,
        createdAt,
        updatedAt,
      ];
}

/// Trip attendance response model
class TripAttendanceResponse extends Equatable {
  final bool? status;
  final String? message;
  final bool? errors;
  final List<StudentAttendanceModel>? attendantStudents;
  final List<StudentAttendanceModel>? absentStudents;

  const TripAttendanceResponse({
    this.status,
    this.message,
    this.errors,
    this.attendantStudents,
    this.absentStudents,
  });

  factory TripAttendanceResponse.fromJson(Map<String, dynamic> json) {
    return TripAttendanceResponse(
      status: json['status'] as bool?,
      message: json['message']?.toString(),
      errors: json['errors'] as bool?,
      attendantStudents: json['attendant_students'] != null
          ? (json['attendant_students'] as List)
              .map((e) => StudentAttendanceModel.fromJson(e))
              .toList()
          : null,
      absentStudents: json['absent_students'] != null
          ? (json['absent_students'] as List)
              .map((e) => StudentAttendanceModel.fromJson(e))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'errors': errors,
      'attendant_students': attendantStudents?.map((e) => e.toJson()).toList(),
      'absent_students': absentStudents?.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        status,
        message,
        errors,
        attendantStudents,
        absentStudents,
      ];
}
