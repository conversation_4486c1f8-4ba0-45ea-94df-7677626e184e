import 'package:equatable/equatable.dart';
import 'student_attendance_model.dart';

/// Route point model for trip route tracking
/// Based on the original SchoolX project structure
class RoutePointModel extends Equatable {
  final int? id;
  final int? tripId;
  final int? busId;
  final String? latitude;
  final String? longitude;
  final String? type;
  final String? createdAt;
  final String? updatedAt;

  const RoutePointModel({
    this.id,
    this.tripId,
    this.busId,
    this.latitude,
    this.longitude,
    this.type,
    this.createdAt,
    this.updatedAt,
  });

  factory RoutePointModel.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return RoutePointModel(
      id: parseInt(json['id']),
      tripId: parseInt(json['trip_id']),
      busId: parseInt(json['bus_id']),
      latitude: json['latitude']?.toString(),
      longitude: json['longitude']?.toString(),
      type: json['type']?.toString(),
      createdAt: json['created_at']?.toString(),
      updatedAt: json['updated_at']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'bus_id': busId,
      'latitude': latitude,
      'longitude': longitude,
      'type': type,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  List<Object?> get props => [
    id,
    tripId,
    busId,
    latitude,
    longitude,
    type,
    createdAt,
    updatedAt,
  ];
}

/// Trip route model containing all route information
class TripRouteModel extends Equatable {
  final int? tripId;
  final String? startTime;
  final String? endTime;
  final double? totalDistance;
  final int? estimatedTime;
  final List<RoutePointModel>? routePoints;

  const TripRouteModel({
    this.tripId,
    this.startTime,
    this.endTime,
    this.totalDistance,
    this.estimatedTime,
    this.routePoints,
  });

  factory TripRouteModel.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    // Helper function to safely parse doubles
    double? parseDouble(dynamic value) {
      if (value == null) return null;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        return double.tryParse(value);
      }
      return null;
    }

    return TripRouteModel(
      tripId: parseInt(json['trip_id']),
      startTime: json['start_time']?.toString(),
      endTime: json['end_time']?.toString(),
      totalDistance: parseDouble(json['total_distance']),
      estimatedTime: parseInt(json['estimated_time']),
      routePoints:
          json['route_points'] != null
              ? (json['route_points'] as List)
                  .map((e) => RoutePointModel.fromJson(e))
                  .toList()
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trip_id': tripId,
      'start_time': startTime,
      'end_time': endTime,
      'total_distance': totalDistance,
      'estimated_time': estimatedTime,
      'route_points': routePoints?.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
    tripId,
    startTime,
    endTime,
    totalDistance,
    estimatedTime,
    routePoints,
  ];
}

/// Trip route response model
class TripRouteResponse extends Equatable {
  final bool? status;
  final String? message;
  final bool? errors;
  final TripRouteModel? data;

  const TripRouteResponse({this.status, this.message, this.errors, this.data});

  factory TripRouteResponse.fromJson(Map<String, dynamic> json) {
    return TripRouteResponse(
      status: json['status'] as bool?,
      message: json['message']?.toString(),
      errors: json['errors'] as bool?,
      data: json['data'] != null ? TripRouteModel.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'errors': errors,
      'data': data?.toJson(),
    };
  }

  @override
  List<Object?> get props => [status, message, errors, data];
}

/// Trip details model combining all trip information
class TripDetailsModel extends Equatable {
  final int? id;
  final String? tripType;
  final String? busName;
  final String? supervisorName;
  final String? driverName;
  final String? date;
  final String? startTime;
  final String? endTime;
  final int? status;
  final List<StudentAttendanceModel>? presentStudents;
  final List<StudentAttendanceModel>? absentStudents;
  final TripRouteModel? routeData;

  const TripDetailsModel({
    this.id,
    this.tripType,
    this.busName,
    this.supervisorName,
    this.driverName,
    this.date,
    this.startTime,
    this.endTime,
    this.status,
    this.presentStudents,
    this.absentStudents,
    this.routeData,
  });

  factory TripDetailsModel.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return TripDetailsModel(
      id: parseInt(json['id']),
      tripType: json['trip_type']?.toString(),
      busName: json['bus_name']?.toString(),
      supervisorName: json['supervisor_name']?.toString(),
      driverName: json['driver_name']?.toString(),
      date: json['date']?.toString(),
      startTime: json['start_time']?.toString(),
      endTime: json['end_time']?.toString(),
      status: parseInt(json['status']),
      presentStudents:
          json['present_students'] != null
              ? (json['present_students'] as List)
                  .map((e) => StudentAttendanceModel.fromJson(e))
                  .toList()
              : null,
      absentStudents:
          json['absent_students'] != null
              ? (json['absent_students'] as List)
                  .map((e) => StudentAttendanceModel.fromJson(e))
                  .toList()
              : null,
      routeData:
          json['route_data'] != null
              ? TripRouteModel.fromJson(json['route_data'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_type': tripType,
      'bus_name': busName,
      'supervisor_name': supervisorName,
      'driver_name': driverName,
      'date': date,
      'start_time': startTime,
      'end_time': endTime,
      'status': status,
      'present_students': presentStudents?.map((e) => e.toJson()).toList(),
      'absent_students': absentStudents?.map((e) => e.toJson()).toList(),
      'route_data': routeData?.toJson(),
    };
  }

  @override
  List<Object?> get props => [
    id,
    tripType,
    busName,
    supervisorName,
    driverName,
    date,
    startTime,
    endTime,
    status,
    presentStudents,
    absentStudents,
    routeData,
  ];
}
