import '../../domain/entities/parent.dart';

/// Parent model for data layer
/// Following Clean Architecture principles
class ParentModel extends Parent {
  const ParentModel({
    required super.id,
    required super.name,
    super.email,
    super.phone,
    super.address,
    super.logoPath,
    super.studentIds,
    super.createdAt,
    super.updatedAt,
  });

  /// Create ParentModel from JSON
  factory ParentModel.fromJson(Map<String, dynamic> json) {
    return ParentModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      email: json['email'],
      phone: json['phone'],
      address: json['address'],
      logoPath: json['logo_path'],
      studentIds: json['student_ids'] != null 
          ? List<int>.from(json['student_ids'])
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  /// Convert ParentModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'logo_path': logoPath,
      'student_ids': studentIds,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create ParentModel from Parent entity
  factory ParentModel.fromEntity(Parent parent) {
    return ParentModel(
      id: parent.id,
      name: parent.name,
      email: parent.email,
      phone: parent.phone,
      address: parent.address,
      logoPath: parent.logoPath,
      studentIds: parent.studentIds,
      createdAt: parent.createdAt,
      updatedAt: parent.updatedAt,
    );
  }

  /// Convert to Parent entity
  Parent toEntity() {
    return Parent(
      id: id,
      name: name,
      email: email,
      phone: phone,
      address: address,
      logoPath: logoPath,
      studentIds: studentIds,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
