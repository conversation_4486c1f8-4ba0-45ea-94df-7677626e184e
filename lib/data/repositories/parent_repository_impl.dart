import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/network/api_service.dart';
import '../../domain/entities/parent.dart';
import '../../domain/repositories/parent_repository.dart';
import '../models/parent_model.dart';

/// Parent repository implementation
/// Following Clean Architecture principles
class ParentRepositoryImpl implements ParentRepository {
  final ApiService _apiService;

  ParentRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<Parent>>> getParents({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        if (search != null && search.isNotEmpty) 'search': search,
      };

      final response = await _apiService.get(
        '/parents',
        queryParameters: queryParams,
      );

      if (response.data['status'] == true) {
        final List<dynamic> parentsJson = response.data['data']['data'] ?? [];
        final parents =
            parentsJson
                .map((json) => ParentModel.fromJson(json).toEntity())
                .toList();
        return Right(parents);
      } else {
        return Left(
          ServerFailure(message: response.data['message'] ?? 'Unknown error'),
        );
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> getParentById(int id) async {
    try {
      final response = await _apiService.get('/parents/$id');

      if (response.data['status'] == true) {
        final parentJson = response.data['data'];
        final parent = ParentModel.fromJson(parentJson).toEntity();
        return Right(parent);
      } else {
        return Left(
          ServerFailure(
            message: response.data['message'] ?? 'Parent not found',
          ),
        );
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> createParent(Parent parent) async {
    try {
      final parentModel = ParentModel.fromEntity(parent);
      final response = await _apiService.post(
        url: '/parents',
        body: parentModel.toJson(),
      );

      if (response.data['status'] == true) {
        final createdParentJson = response.data['data'];
        final createdParent =
            ParentModel.fromJson(createdParentJson).toEntity();
        return Right(createdParent);
      } else {
        return Left(
          ServerFailure(
            message: response.data['message'] ?? 'Failed to create parent',
          ),
        );
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> updateParent(Parent parent) async {
    try {
      final parentModel = ParentModel.fromEntity(parent);
      final response = await _apiService.put(
        url: '/parents/${parent.id}',
        body: parentModel.toJson(),
      );

      if (response.data['status'] == true) {
        final updatedParentJson = response.data['data'];
        final updatedParent =
            ParentModel.fromJson(updatedParentJson).toEntity();
        return Right(updatedParent);
      } else {
        return Left(
          ServerFailure(
            message: response.data['message'] ?? 'Failed to update parent',
          ),
        );
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteParent(int id) async {
    try {
      final response = await _apiService.delete(url: '/parents/$id');

      if (response.data['status'] == true) {
        return const Right(true);
      } else {
        return Left(
          ServerFailure(
            message: response.data['message'] ?? 'Failed to delete parent',
          ),
        );
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<int>>> getStudentsByParentId(int parentId) async {
    try {
      final response = await _apiService.get('/parents/$parentId/students');

      if (response.data['status'] == true) {
        final List<dynamic> studentsJson = response.data['data'] ?? [];
        final studentIds =
            studentsJson.map((json) => json['id'] as int).toList();
        return Right(studentIds);
      } else {
        return Left(
          ServerFailure(response.data['message'] ?? 'Failed to get students'),
        );
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
