import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/network/api_service.dart';
import '../../domain/entities/parent.dart';
import '../../domain/repositories/parent_repository.dart';
import '../models/parent_model.dart';

/// Parent repository implementation
/// Following Clean Architecture principles
class ParentRepositoryImpl implements ParentRepository {
  final ApiService _apiService;

  ParentRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<Parent>>> getParents({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      // Build URL with pagination (matching original SchoolX format)
      String url = 'parents/all/admins?page=$page&limit=$limit';
      if (search != null && search.isNotEmpty) {
        url += '&search=$search';
      }

      final response = await _apiService.get(url, isAuth: true);

      final responseData = response.data;

      // Check for API errors (matching original SchoolX format)
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to fetch parents';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse parents data (matching original SchoolX response structure)
      final parentsData = responseData['data']?['data'] as List<dynamic>?;
      if (parentsData == null) {
        return const Right([]);
      }

      final parents =
          parentsData
              .map((json) => ParentModel.fromJson(json as Map<String, dynamic>))
              .map((model) => model.toEntity())
              .toList();

      return Right(parents);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> getParentById(int id) async {
    try {
      final response = await _apiService.get(
        'parents/show/admins/$id',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Parent not found';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse parent data
      final parentData = responseData['data'];
      if (parentData == null) {
        return Left(ServerFailure(message: 'Parent not found'));
      }

      final parent =
          ParentModel.fromJson(parentData as Map<String, dynamic>).toEntity();
      return Right(parent);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> createParent(Parent parent) async {
    try {
      final parentModel = ParentModel.fromEntity(parent);
      final response = await _apiService.post(
        url: 'parents/store/admins',
        body: parentModel.toJson(),
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to create parent';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse created parent data
      final createdParentData = responseData['data'];
      if (createdParentData == null) {
        return Left(ServerFailure(message: 'Failed to create parent'));
      }

      final createdParent =
          ParentModel.fromJson(
            createdParentData as Map<String, dynamic>,
          ).toEntity();
      return Right(createdParent);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> updateParent(Parent parent) async {
    try {
      final parentModel = ParentModel.fromEntity(parent);
      final response = await _apiService.put(
        url: 'parents/update/admins/${parent.id}',
        body: parentModel.toJson(),
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to update parent';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse updated parent data
      final updatedParentData = responseData['data'];
      if (updatedParentData == null) {
        return Left(ServerFailure(message: 'Failed to update parent'));
      }

      final updatedParent =
          ParentModel.fromJson(
            updatedParentData as Map<String, dynamic>,
          ).toEntity();
      return Right(updatedParent);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteParent(int id) async {
    try {
      final response = await _apiService.delete(
        url: 'parents/destroy/admins/$id',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to delete parent';
        return Left(ServerFailure(message: errorMessage));
      }

      return const Right(true);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<int>>> getStudentsByParentId(int parentId) async {
    try {
      final response = await _apiService.get(
        'parents/$parentId/students/admins',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to get students';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse students data
      final studentsData = responseData['data'] as List<dynamic>?;
      if (studentsData == null) {
        return const Right([]);
      }

      final studentIds =
          studentsData
              .map((json) => (json as Map<String, dynamic>)['id'] as int)
              .toList();

      return Right(studentIds);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>>
  getAvailableSchools() async {
    try {
      final response = await _apiService.get(
        'schools/all/admins',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get schools';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse schools data
      final schoolsData = responseData['data'] as List<dynamic>?;
      if (schoolsData == null) {
        return const Right([]);
      }

      final schools =
          schoolsData
              .map(
                (json) => {
                  'id': (json as Map<String, dynamic>)['id'],
                  'name': json['name'],
                },
              )
              .toList();

      return Right(schools);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenderOptions() async {
    try {
      final response = await _apiService.get(
        'genders/all/admins',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get genders';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse genders data
      final gendersData = responseData['data'] as List<dynamic>?;
      if (gendersData == null) {
        return const Right([]);
      }

      final genders =
          gendersData
              .map(
                (json) => {
                  'id': (json as Map<String, dynamic>)['id'],
                  'name': json['name'],
                },
              )
              .toList();

      return Right(genders);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>>
  getReligionOptions() async {
    try {
      final response = await _apiService.get(
        'religions/all/admins',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to get religions';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse religions data
      final religionsData = responseData['data'] as List<dynamic>?;
      if (religionsData == null) {
        return const Right([]);
      }

      final religions =
          religionsData
              .map(
                (json) => {
                  'id': (json as Map<String, dynamic>)['id'],
                  'name': json['name'],
                },
              )
              .toList();

      return Right(religions);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
