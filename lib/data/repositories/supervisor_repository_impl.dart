import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/network/api_service.dart';
import '../../domain/entities/supervisor.dart';
import '../../domain/repositories/supervisor_repository.dart';
import '../models/supervisor_model.dart';

/// Supervisor repository implementation
/// Following Single Responsibility Principle by focusing only on supervisor data operations
class SupervisorRepositoryImpl implements SupervisorRepository {
  final ApiService _apiService;

  SupervisorRepositoryImpl({required ApiService apiService})
      : _apiService = apiService;

  @override
  Future<Either<Failure, List<Supervisor>>> getSupervisors({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      LoggerService.debug('Fetching supervisors', data: {
        'page': page,
        'limit': limit,
      });

      // Build URL with pagination (matching original SchoolX format)
      String url = 'attendants/all/admins?page=$page&limit=$limit';

      final response = await _apiService.get(
        url,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received supervisors response', data: responseData);

      // Check for API errors (matching original SchoolX format)
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch supervisors';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse supervisors data (matching original SchoolX response structure)
      final supervisorsData = responseData['data']?['data'] as List<dynamic>?;
      if (supervisorsData == null) {
        LoggerService.warning('No supervisors data found in response');
        return const Right([]);
      }

      final supervisors = supervisorsData
          .map((json) => SupervisorModel.fromJson(json as Map<String, dynamic>))
          .toList();

      LoggerService.info('Successfully fetched ${supervisors.length} supervisors');
      return Right(supervisors);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while fetching supervisors', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while fetching supervisors', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Supervisor>> getSupervisorById(int id) async {
    try {
      LoggerService.debug('Fetching supervisor by ID', data: {'supervisorId': id});

      final response = await _apiService.get(
        'attendants/show/admins/$id',
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received supervisor response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Supervisor not found';
        return Left(ServerFailure(message: errorMessage));
      }

      final supervisorData = responseData['data'];
      if (supervisorData == null) {
        return Left(ServerFailure(message: 'Supervisor not found'));
      }

      final supervisor = SupervisorModel.fromJson(supervisorData as Map<String, dynamic>);
      LoggerService.info('Successfully fetched supervisor', data: {'supervisorId': id});
      return Right(supervisor);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while fetching supervisor', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while fetching supervisor', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Supervisor>> createSupervisor(Supervisor supervisor) async {
    try {
      LoggerService.debug('Creating supervisor', data: {'supervisorName': supervisor.name});

      // Prepare request data
      final requestData = {
        'name': supervisor.name,
        'username': supervisor.username,
        'email': supervisor.email,
        'phone': supervisor.phone,
        'address': supervisor.address,
        'city_name': supervisor.cityName,
        'birth_date': supervisor.birthDate,
        'Joining_Date': supervisor.joiningDate,
        'gender_id': supervisor.genderId,
        'religion_id': supervisor.religionId,
        'bus_id': supervisor.busId,
        'status': supervisor.status ?? 1,
      };

      final response = await _apiService.post(
        url: 'attendants/store/admins',
        body: requestData,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received create supervisor response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to create supervisor';
        return Left(ServerFailure(message: errorMessage));
      }

      final createdSupervisorData = responseData['data'];
      if (createdSupervisorData == null) {
        return Left(ServerFailure(message: 'Failed to create supervisor'));
      }

      final createdSupervisor = SupervisorModel.fromJson(createdSupervisorData as Map<String, dynamic>);
      LoggerService.info('Successfully created supervisor', data: {'supervisorId': createdSupervisor.id});
      return Right(createdSupervisor);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while creating supervisor', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while creating supervisor', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Supervisor>> updateSupervisor(Supervisor supervisor) async {
    try {
      LoggerService.debug('Updating supervisor', data: {'supervisorId': supervisor.id});

      // Prepare request data
      final requestData = {
        'name': supervisor.name,
        'username': supervisor.username,
        'email': supervisor.email,
        'phone': supervisor.phone,
        'address': supervisor.address,
        'city_name': supervisor.cityName,
        'birth_date': supervisor.birthDate,
        'Joining_Date': supervisor.joiningDate,
        'gender_id': supervisor.genderId,
        'religion_id': supervisor.religionId,
        'bus_id': supervisor.busId,
        'status': supervisor.status ?? 1,
      };

      final response = await _apiService.post(
        url: 'attendants/update/admins/${supervisor.id}',
        body: requestData,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received update supervisor response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to update supervisor';
        return Left(ServerFailure(message: errorMessage));
      }

      final updatedSupervisorData = responseData['data'];
      if (updatedSupervisorData == null) {
        return Left(ServerFailure(message: 'Failed to update supervisor'));
      }

      final updatedSupervisor = SupervisorModel.fromJson(updatedSupervisorData as Map<String, dynamic>);
      LoggerService.info('Successfully updated supervisor', data: {'supervisorId': supervisor.id});
      return Right(updatedSupervisor);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while updating supervisor', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while updating supervisor', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteSupervisor(int id) async {
    try {
      LoggerService.debug('Deleting supervisor', data: {'supervisorId': id});

      // Use the exact same endpoint format as original SchoolX app
      final response = await _apiService.delete(
        url: 'attendants/destroy/$id',
        body: {},
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received delete supervisor response', data: responseData);

      // Check response format exactly like original SchoolX app
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to delete supervisor';
        LoggerService.error('API returned error for supervisor deletion', data: {
          'supervisorId': id,
          'error': errorMessage,
        });
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.info('Successfully deleted supervisor', data: {
        'supervisorId': id,
      });
      return const Right(true);
    } catch (e) {
      LoggerService.error('Server exception while deleting supervisor', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getAvailableBuses() async {
    try {
      LoggerService.debug('Fetching available buses for supervisor assignment');

      final response = await _apiService.get(
        'buses/show/availableAdd/admins',
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug('Received available buses response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch available buses';
        return Left(ServerFailure(message: errorMessage));
      }

      final busesData = responseData['data'] as List<dynamic>?;
      if (busesData == null) {
        return const Right([]);
      }

      final buses = busesData.cast<Map<String, dynamic>>();
      LoggerService.info('Successfully fetched ${buses.length} available buses');
      return Right(buses);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while fetching available buses', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while fetching available buses', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenderOptions() async {
    try {
      LoggerService.debug('Fetching gender options');

      final response = await _apiService.get(
        'general/gender',
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch gender options';
        return Left(ServerFailure(message: errorMessage));
      }

      final genderData = responseData['data'] as List<dynamic>?;
      if (genderData == null) {
        return const Right([]);
      }

      final genders = genderData.cast<Map<String, dynamic>>();
      return Right(genders);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getReligionOptions() async {
    try {
      LoggerService.debug('Fetching religion options');

      final response = await _apiService.get(
        'general/religion',
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to fetch religion options';
        return Left(ServerFailure(message: errorMessage));
      }

      final religionData = responseData['data'] as List<dynamic>?;
      if (religionData == null) {
        return const Right([]);
      }

      final religions = religionData.cast<Map<String, dynamic>>();
      return Right(religions);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }


}
