import 'package:dartz/dartz.dart';
import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/user_repository.dart';
import '../datasources/user_local_data_source.dart';
import '../datasources/user_remote_data_source.dart';
import '../models/user_model.dart';

/// UserRepositoryImpl class that implements the UserRepository interface
/// Following Dependency Inversion Principle by depending on abstractions
class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSource remoteDataSource;
  final UserLocalDataSource localDataSource;

  UserRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, User>> login(String email, String password) async {
    try {
      final userModel = await remoteDataSource.login(email, password);
      await localDataSource.cacheUser(userModel);
      if (userModel.token != null) {
        await localDataSource.cacheToken(userModel.token!);
      }
      return Right(userModel);
    } catch (e) {
      if (e is UnauthorizedException) {
        return Left(AuthFailure(message: e.message));
      } else if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      } else if (e is NetworkException) {
        return Left(NetworkFailure(message: e.message));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> register(User user, String password) async {
    try {
      final userModel = await remoteDataSource.register(
        UserModel(
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          phone: user.phone,
          type: user.type,
          address: user.address,
          cityName: user.cityName,
          logo: user.logo,
          logoPath: user.logoPath,
          latitude: user.latitude,
          longitude: user.longitude,
          schoolId: user.schoolId,
          isVerified: user.isVerified,
        ),
        password,
      );
      await localDataSource.cacheUser(userModel);
      if (userModel.token != null) {
        await localDataSource.cacheToken(userModel.token!);
      }
      return Right(userModel);
    } catch (e) {
      if (e is UnauthorizedException) {
        return Left(AuthFailure(message: e.message));
      } else if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      } else if (e is NetworkException) {
        return Left(NetworkFailure(message: e.message));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> getCurrentUser() async {
    try {
      final userModel = await localDataSource.getCachedUser();
      return Right(userModel);
    } catch (e) {
      try {
        final userModel = await remoteDataSource.getCurrentUser();
        await localDataSource.cacheUser(userModel);
        return Right(userModel);
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    }
  }

  @override
  Future<Either<Failure, bool>> logout() async {
    try {
      await remoteDataSource.logout();
      await localDataSource.clearCachedUser();
      return const Right(true);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<User>>> getAll() async {
    try {
      final users = await remoteDataSource.getUsers();
      return Right(users);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> getById(String id) async {
    try {
      final user = await remoteDataSource.getUserById(id);
      return Right(user);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> create(User item) async {
    try {
      final userModel = await remoteDataSource.register(
        UserModel(
          id: item.id,
          name: item.name,
          email: item.email,
          role: item.role,
          phone: item.phone,
          type: item.type,
          address: item.address,
          cityName: item.cityName,
          logo: item.logo,
          logoPath: item.logoPath,
          latitude: item.latitude,
          longitude: item.longitude,
          schoolId: item.schoolId,
          isVerified: item.isVerified,
        ),
        'defaultPassword', // This would be handled differently in a real app
      );
      return Right(userModel);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      } else if (e is ServerException) {
        return Left(ServerFailure(message: e.message));
      } else if (e is NetworkException) {
        return Left(NetworkFailure(message: e.message));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> update(User item) async {
    try {
      final userModel = await remoteDataSource.updateUser(
        UserModel(
          id: item.id,
          name: item.name,
          email: item.email,
          role: item.role,
          phone: item.phone,
          type: item.type,
          address: item.address,
          cityName: item.cityName,
          logo: item.logo,
          logoPath: item.logoPath,
          latitude: item.latitude,
          longitude: item.longitude,
          schoolId: item.schoolId,
          isVerified: item.isVerified,
        ),
      );
      return Right(userModel);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      } else if (e is NotFoundException) {
        return Left(NotFoundFailure(message: e.message));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> delete(String id) async {
    try {
      final result = await remoteDataSource.deleteUser(id);
      return Right(result);
    } catch (e) {
      if (e is NotFoundException) {
        return Left(NotFoundFailure(message: e.message));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> forgotPassword(String email) async {
    try {
      final result = await remoteDataSource.forgotPassword(email);
      return Right(result);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> resetPassword(
    String email,
    String code,
    String password,
  ) async {
    try {
      final result = await remoteDataSource.resetPassword(
        email,
        code,
        password,
      );
      return Right(result);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> verifyEmail(String email, String code) async {
    try {
      final result = await remoteDataSource.verifyEmail(email, code);
      return Right(result);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> resendVerificationCode(String email) async {
    try {
      final result = await remoteDataSource.resendVerificationCode(email);
      return Right(result);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> updateProfile(User user) async {
    try {
      final userModel = await remoteDataSource.updateProfile(
        UserModel(
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          phone: user.phone,
          type: user.type,
          address: user.address,
          cityName: user.cityName,
          logo: user.logo,
          logoPath: user.logoPath,
          latitude: user.latitude,
          longitude: user.longitude,
          schoolId: user.schoolId,
          isVerified: user.isVerified,
        ),
      );
      await localDataSource.cacheUser(userModel);
      return Right(userModel);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      } else if (e is NotFoundException) {
        return Left(NotFoundFailure(message: e.message));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      final result = await remoteDataSource.changePassword(
        currentPassword,
        newPassword,
      );
      return Right(result);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      } else if (e is UnauthorizedException) {
        return Left(AuthFailure(message: e.message));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isAuthenticated() async {
    try {
      final result = await localDataSource.isAuthenticated();
      return Right(result);
    } catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> uploadProfileImage(
    String userId,
    String imagePath,
  ) async {
    try {
      final imageUrl = await remoteDataSource.uploadProfileImage(
        userId,
        imagePath,
      );
      return Right(imageUrl);
    } catch (e) {
      if (e is ValidationException) {
        return Left(ValidationFailure(message: e.message, errors: e.errors));
      } else if (e is NotFoundException) {
        return Left(NotFoundFailure(message: e.message));
      } else if (e is NetworkException) {
        return Left(NetworkFailure(message: e.message));
      }
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
