import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/student.dart';

/// Widget for selecting students for a trip
/// Following Single Responsibility Principle by focusing only on student selection UI
class StudentSelectionWidget extends StatefulWidget {
  final List<Student> students;
  final List<Student> selectedStudents;
  final Function(Student) onStudentToggle;

  const StudentSelectionWidget({
    super.key,
    required this.students,
    required this.selectedStudents,
    required this.onStudentToggle,
  });

  @override
  State<StudentSelectionWidget> createState() => _StudentSelectionWidgetState();
}

class _StudentSelectionWidgetState extends State<StudentSelectionWidget> {
  final TextEditingController _searchController = TextEditingController();
  List<Student> _filteredStudents = [];

  @override
  void initState() {
    super.initState();
    _filteredStudents = widget.students;
    _searchController.addListener(_filterStudents);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterStudents() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredStudents = widget.students.where((student) {
        return (student.name?.toLowerCase().contains(query) ?? false) ||
               (student.phone?.toLowerCase().contains(query) ?? false);
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchField(),
        const SizedBox(height: 16),
        _buildActionButtons(),
        const SizedBox(height: 16),
        _buildStudentsList(),
      ],
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'البحث عن طالب...',
        prefixIcon: const Icon(Icons.search_rounded),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _selectAll,
            icon: const Icon(Icons.select_all_rounded),
            label: const Text('تحديد الكل'),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _deselectAll,
            icon: const Icon(Icons.deselect_rounded),
            label: const Text('إلغاء التحديد'),
          ),
        ),
      ],
    );
  }

  Widget _buildStudentsList() {
    if (_filteredStudents.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.people_outline_rounded,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد طلاب',
              style: Get.textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على طلاب مطابقين للبحث',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      height: 300,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListView.builder(
        itemCount: _filteredStudents.length,
        itemBuilder: (context, index) {
          final student = _filteredStudents[index];
          final isSelected = widget.selectedStudents.contains(student);

          return _buildStudentTile(student, isSelected);
        },
      ),
    );
  }

  Widget _buildStudentTile(Student student, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? ColorConstants.primary.withAlpha(26) : null,
        borderRadius: BorderRadius.circular(8),
        border: isSelected
            ? Border.all(color: ColorConstants.primary.withAlpha(128))
            : null,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isSelected
              ? ColorConstants.primary
              : Colors.grey[300],
          child: isSelected
              ? const Icon(Icons.check, color: Colors.white, size: 18)
              : Text(
                  (student.name?.isNotEmpty == true)
                      ? student.name![0].toUpperCase()
                      : '؟',
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
        ),
        title: Text(
          student.name ?? 'غير محدد',
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? ColorConstants.primary : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (student.phone?.isNotEmpty == true)
              Text(
                'الهاتف: ${student.phone}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            if (student.address?.isNotEmpty == true)
              Text(
                'العنوان: ${student.address}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: isSelected
            ? Icon(
                Icons.check_circle_rounded,
                color: ColorConstants.primary,
              )
            : Icon(
                Icons.radio_button_unchecked_rounded,
                color: Colors.grey[400],
              ),
        onTap: () => widget.onStudentToggle(student),
      ),
    );
  }

  void _selectAll() {
    for (final student in _filteredStudents) {
      if (!widget.selectedStudents.contains(student)) {
        widget.onStudentToggle(student);
      }
    }
  }

  void _deselectAll() {
    final studentsToRemove = widget.selectedStudents
        .where((selected) => _filteredStudents.contains(selected))
        .toList();
    
    for (final student in studentsToRemove) {
      widget.onStudentToggle(student);
    }
  }
}
