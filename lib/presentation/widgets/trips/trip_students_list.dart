import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../domain/entities/trip.dart';

/// TripStudentsList widget for displaying trip students
/// Following Single Responsibility Principle by focusing only on students list display
class TripStudentsList extends StatelessWidget {
  final Trip trip;

  const TripStudentsList({
    super.key,
    required this.trip,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          _buildHeader(),
          
          // Students list
          _buildStudentsList(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.people_rounded,
            color: Get.theme.primaryColor,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            'قائمة الطلاب',
            style: Get.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Get.theme.primaryColor.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${trip.studentIds.length} طالب',
              style: Get.textTheme.bodySmall?.copyWith(
                color: Get.theme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentsList() {
    if (trip.studentIds.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      constraints: const BoxConstraints(maxHeight: 400),
      child: Column(
        children: [
          // Attendance summary
          _buildAttendanceSummary(),
          
          // Students list
          Expanded(
            child: ListView.builder(
              itemCount: trip.studentIds.length,
              itemBuilder: (context, index) {
                final studentId = trip.studentIds[index];
                final isAttended = trip.attendedStudentIds.contains(studentId);
                return _buildStudentItem(studentId, isAttended, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceSummary() {
    final attendedCount = trip.attendedStudentIds.length;
    final totalCount = trip.studentIds.length;
    final absentCount = totalCount - attendedCount;
    final attendancePercentage = totalCount > 0 ? (attendedCount / totalCount * 100).round() : 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryItem(
              icon: Icons.check_circle_rounded,
              label: 'حاضر',
              value: '$attendedCount',
              color: Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryItem(
              icon: Icons.cancel_rounded,
              label: 'غائب',
              value: '$absentCount',
              color: Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryItem(
              icon: Icons.percent_rounded,
              label: 'نسبة الحضور',
              value: '$attendancePercentage%',
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Get.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Get.textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildStudentItem(String studentId, bool isAttended, int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: index.isEven ? Colors.white : Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[100]!),
        ),
      ),
      child: Row(
        children: [
          // Attendance status icon
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: isAttended 
                  ? Colors.green.withAlpha(26) // 0.1 * 255 = 26
                  : Colors.red.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              isAttended ? Icons.check_rounded : Icons.close_rounded,
              color: isAttended ? Colors.green : Colors.red,
              size: 16,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Student info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'طالب #$studentId', // In real app, this would be student name
                  style: Get.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  isAttended ? 'حاضر' : 'غائب',
                  style: Get.textTheme.bodySmall?.copyWith(
                    color: isAttended ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          
          // Status badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isAttended 
                  ? Colors.green.withAlpha(26) // 0.1 * 255 = 26
                  : Colors.red.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              isAttended ? 'حاضر' : 'غائب',
              style: Get.textTheme.bodySmall?.copyWith(
                color: isAttended ? Colors.green[800] : Colors.red[800],
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.person_off_rounded,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد طلاب',
            style: Get.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم تسجيل أي طلاب في هذه الرحلة',
            style: Get.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
