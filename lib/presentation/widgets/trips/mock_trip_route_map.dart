import 'dart:math';
import 'package:flutter/material.dart';
import '../../../data/models/route_model.dart';
import '../../../data/models/student_attendance_model.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';

/// Mock Trip Route Map Widget
/// A fallback widget when Google Maps is not available or API key is not configured
/// Shows a visual representation of the route and student locations
class MockTripRouteMap extends StatelessWidget {
  final TripRouteModel? routeData;
  final List<StudentAttendanceModel>? presentStudents;
  final List<StudentAttendanceModel>? absentStudents;
  final double height;
  final VoidCallback? onMapCreated;

  const MockTripRouteMap({
    super.key,
    required this.routeData,
    this.presentStudents,
    this.absentStudents,
    this.height = 400,
    this.onMapCreated,
  });

  @override
  Widget build(BuildContext context) {
    // If no route data, create sample data for demonstration
    final effectiveRouteData = routeData ?? _createSampleRouteData();

    if (effectiveRouteData.routePoints == null ||
        effectiveRouteData.routePoints!.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with stats
          _buildHeader(),

          // Mock Map
          Expanded(child: _buildMockMap(effectiveRouteData)),

          // Route info
          _buildRouteInfo(effectiveRouteData),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[600]!, Colors.blue[700]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.map_rounded, color: Colors.white, size: 28),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'خريطة المسار التفاعلية',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.getFontSize(18),
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'عرض تجريبي للمسار ومواقع الطلاب',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.getFontSize(12),
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildLegend(),
        ],
      ),
    );
  }

  Widget _buildLegend() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Wrap(
        spacing: 12,
        runSpacing: 8,
        children: [
          _buildLegendItem(
            color: Colors.white,
            label: 'بداية',
            icon: Icons.play_arrow,
            backgroundColor: Colors.green,
          ),
          _buildLegendItem(
            color: Colors.white,
            label: 'مسار',
            icon: Icons.location_on,
            backgroundColor: Colors.blue,
          ),
          _buildLegendItem(
            color: Colors.white,
            label: 'نهاية',
            icon: Icons.stop,
            backgroundColor: Colors.red,
          ),
          if (presentStudents != null && presentStudents!.isNotEmpty)
            _buildLegendItem(
              color: Colors.white,
              label: 'حاضر',
              icon: Icons.person,
              backgroundColor: Colors.green,
            ),
          if (absentStudents != null && absentStudents!.isNotEmpty)
            _buildLegendItem(
              color: Colors.white,
              label: 'غائب',
              icon: Icons.person_off,
              backgroundColor: Colors.orange,
            ),
        ],
      ),
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required String label,
    required IconData icon,
    Color? backgroundColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: ResponsiveUtils.getFontSize(12),
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMockMap(TripRouteModel routeData) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Stack(
          children: [
            // Background pattern
            _buildMapBackground(),

            // Route visualization
            _buildRouteVisualization(routeData),

            // Student markers
            _buildStudentMarkers(),

            // Info overlay
            _buildInfoOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildMapBackground() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue[50]!, Colors.green[50]!],
        ),
      ),
      child: CustomPaint(painter: GridPainter(), size: Size.infinite),
    );
  }

  Widget _buildRouteVisualization(TripRouteModel routeData) {
    final routePoints = routeData.routePoints!;

    return Positioned.fill(
      child: CustomPaint(painter: RoutePainter(routePoints)),
    );
  }

  Widget _buildStudentMarkers() {
    return Positioned.fill(
      child: Stack(
        children: [
          // Present students
          if (presentStudents != null)
            ...presentStudents!.asMap().entries.map((entry) {
              final index = entry.key;
              final student = entry.value;
              return _buildStudentMarker(
                student,
                true,
                index,
                presentStudents!.length,
              );
            }),

          // Absent students
          if (absentStudents != null)
            ...absentStudents!.asMap().entries.map((entry) {
              final index = entry.key;
              final student = entry.value;
              return _buildStudentMarker(
                student,
                false,
                index,
                absentStudents!.length,
              );
            }),
        ],
      ),
    );
  }

  Widget _buildStudentMarker(
    StudentAttendanceModel student,
    bool isPresent,
    int index,
    int total,
  ) {
    // Calculate position based on index
    final double left = 50 + (index * 30.0) % 200;
    final double top = 50 + (index * 40.0) % 150;

    return Positioned(
      left: left,
      top: top,
      child: Tooltip(
        message:
            '${isPresent ? '✅' : '❌'} ${student.name ?? 'طالب'}\n${student.grade ?? ''}',
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isPresent ? Colors.green : Colors.orange,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            isPresent ? Icons.check : Icons.close,
            color: Colors.white,
            size: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoOverlay() {
    return Positioned(
      top: 16,
      left: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'عرض تجريبي للخريطة',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(12),
                fontWeight: FontWeight.bold,
                color: ColorConstants.primary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'للحصول على خريطة حقيقية، قم بإعداد Google Maps API',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(10),
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRouteInfo(TripRouteModel routeData) {
    final presentCount = presentStudents?.length ?? 0;
    final absentCount = absentStudents?.length ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          // Route statistics
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildInfoItem(
                icon: Icons.route,
                label: 'نقاط المسار',
                value: '${routeData.routePoints?.length ?? 0}',
                color: ColorConstants.primary,
              ),
              _buildInfoItem(
                icon: Icons.straighten,
                label: 'المسافة',
                value:
                    '${(routeData.totalDistance ?? 0).toStringAsFixed(1)} كم',
                color: Colors.orange,
              ),
              _buildInfoItem(
                icon: Icons.access_time,
                label: 'الوقت',
                value: '${routeData.estimatedTime ?? 0} د',
                color: Colors.green,
              ),
            ],
          ),

          // Student statistics (if available)
          if (presentCount > 0 || absentCount > 0) ...[
            const SizedBox(height: 12),
            const Divider(height: 1),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildInfoItem(
                  icon: Icons.people,
                  label: 'إجمالي الطلاب',
                  value: '${presentCount + absentCount}',
                  color: Colors.blue,
                ),
                _buildInfoItem(
                  icon: Icons.check_circle,
                  label: 'حاضرين',
                  value: '$presentCount',
                  color: Colors.green,
                ),
                _buildInfoItem(
                  icon: Icons.cancel,
                  label: 'غائبين',
                  value: '$absentCount',
                  color: Colors.red,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: ResponsiveUtils.getFontSize(14),
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: ResponsiveUtils.getFontSize(12),
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.map_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات مسار',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(16),
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على نقاط المسار لهذه الرحلة',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(14),
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Create sample route data for demonstration
  TripRouteModel _createSampleRouteData() {
    return TripRouteModel(
      tripId: 1,
      startTime:
          DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
      endTime:
          DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
      totalDistance: 15.5,
      estimatedTime: 45,
      routePoints: [
        RoutePointModel(
          id: 1,
          tripId: 1,
          latitude: '30.0444',
          longitude: '31.2357',
          type: 'start',
          createdAt:
              DateTime.now()
                  .subtract(const Duration(hours: 2))
                  .toIso8601String(),
        ),
        RoutePointModel(
          id: 2,
          tripId: 1,
          latitude: '30.0500',
          longitude: '31.2400',
          type: 'intermediate',
          createdAt:
              DateTime.now()
                  .subtract(const Duration(hours: 2, minutes: 15))
                  .toIso8601String(),
        ),
        RoutePointModel(
          id: 3,
          tripId: 1,
          latitude: '30.0600',
          longitude: '31.2500',
          type: 'intermediate',
          createdAt:
              DateTime.now()
                  .subtract(const Duration(hours: 1, minutes: 45))
                  .toIso8601String(),
        ),
        RoutePointModel(
          id: 4,
          tripId: 1,
          latitude: '30.0700',
          longitude: '31.2600',
          type: 'end',
          createdAt:
              DateTime.now()
                  .subtract(const Duration(hours: 1))
                  .toIso8601String(),
        ),
      ],
    );
  }
}

/// Custom painter for grid background
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.grey.withOpacity(0.1)
          ..strokeWidth = 1;

    const gridSize = 20.0;

    // Draw vertical lines
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter for route visualization
class RoutePainter extends CustomPainter {
  final List<RoutePointModel> routePoints;

  RoutePainter(this.routePoints);

  @override
  void paint(Canvas canvas, Size size) {
    if (routePoints.length < 2) return;

    // Draw route line
    final routePaint =
        Paint()
          ..color = Colors.blue[600]!
          ..strokeWidth = 4
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round;

    final path = Path();

    // Create a more realistic curved path
    for (int i = 0; i < routePoints.length; i++) {
      final progress = i / (routePoints.length - 1);
      final x = 40 + progress * (size.width - 80);
      final y =
          size.height * 0.2 +
          (sin(progress * pi * 2) * 30) +
          (progress * size.height * 0.4);

      if (i == 0) {
        path.moveTo(x, y);
        // Draw start marker
        _drawMarker(canvas, Offset(x, y), Colors.green, 16.0);
      } else if (i == routePoints.length - 1) {
        path.lineTo(x, y);
        // Draw end marker
        _drawMarker(canvas, Offset(x, y), Colors.red, 16.0);
      } else {
        path.lineTo(x, y);
        // Draw intermediate marker
        _drawMarker(canvas, Offset(x, y), Colors.blue, 12.0);
      }
    }

    canvas.drawPath(path, routePaint);
  }

  void _drawMarker(Canvas canvas, Offset position, Color color, double radius) {
    final paint = Paint()..color = color;

    // Draw marker circle
    canvas.drawCircle(position, radius, paint);

    // Draw white border
    final borderPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;
    canvas.drawCircle(position, radius, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
