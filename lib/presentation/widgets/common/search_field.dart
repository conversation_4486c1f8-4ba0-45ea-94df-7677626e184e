import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';

/// Search field widget for filtering data
/// Following Single Responsibility Principle by focusing only on search input
class SearchField extends StatefulWidget {
  final String hint;
  final Function(String) onChanged;
  final String? initialValue;
  final IconData? prefixIcon;
  final bool enabled;

  const SearchField({
    super.key,
    required this.hint,
    required this.onChanged,
    this.initialValue,
    this.prefixIcon,
    this.enabled = true,
  });

  @override
  State<SearchField> createState() => _SearchFieldState();
}

class _SearchFieldState extends State<SearchField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? ColorConstants.surfaceDark
            : ColorConstants.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.textSecondaryDark.withValues(alpha: 0.3)
              : ColorConstants.textSecondary.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _controller,
        enabled: widget.enabled,
        onChanged: widget.onChanged,
        decoration: InputDecoration(
          hintText: widget.hint,
          hintStyle: TextStyle(
            color: Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.textSecondaryDark
                : ColorConstants.textSecondary,
          ),
          prefixIcon: Icon(
            widget.prefixIcon ?? Icons.search,
            color: Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.textSecondaryDark
                : ColorConstants.textSecondary,
          ),
          suffixIcon: _controller.text.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textSecondaryDark
                        : ColorConstants.textSecondary,
                  ),
                  onPressed: () {
                    _controller.clear();
                    widget.onChanged('');
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        style: TextStyle(
          color: Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.textPrimaryDark
              : ColorConstants.textPrimary,
        ),
      ),
    );
  }
}
