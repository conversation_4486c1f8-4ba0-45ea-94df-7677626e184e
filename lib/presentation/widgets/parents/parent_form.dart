import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/parent.dart';

/// Parent form widget for creating and editing parents
/// Following Clean Architecture principles
class ParentForm extends StatefulWidget {
  final Parent? parent;
  final Function(Map<String, dynamic>) onSubmit;
  final bool isLoading;

  const ParentForm({
    super.key,
    this.parent,
    required this.onSubmit,
    this.isLoading = false,
  });

  @override
  State<ParentForm> createState() => _ParentFormState();
}

class _ParentFormState extends State<ParentForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.parent != null) {
      _nameController.text = widget.parent!.name ?? '';
      _emailController.text = widget.parent!.email ?? '';
      _phoneController.text = widget.parent!.phone ?? '';
      _addressController.text = widget.parent!.address ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildNameField(),
          const SizedBox(height: 16),
          _buildEmailField(),
          const SizedBox(height: 16),
          _buildPhoneField(),
          const SizedBox(height: 16),
          _buildAddressField(),
          const SizedBox(height: 32),
          _buildSubmitButton(),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'الاسم *',
        hintText: 'أدخل اسم ولي الأمر',
        prefixIcon: const Icon(Icons.person),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'الاسم مطلوب';
        }
        if (value.trim().length < 2) {
          return 'الاسم يجب أن يكون أكثر من حرفين';
        }
        return null;
      },
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      decoration: InputDecoration(
        labelText: 'البريد الإلكتروني',
        hintText: 'أدخل البريد الإلكتروني',
        prefixIcon: const Icon(Icons.email),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: TextInputType.emailAddress,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
          if (!emailRegex.hasMatch(value)) {
            return 'البريد الإلكتروني غير صحيح';
          }
        }
        return null;
      },
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildPhoneField() {
    return TextFormField(
      controller: _phoneController,
      decoration: InputDecoration(
        labelText: 'رقم الهاتف',
        hintText: 'أدخل رقم الهاتف',
        prefixIcon: const Icon(Icons.phone),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: TextInputType.phone,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final phoneRegex = RegExp(r'^[0-9+\-\s()]+$');
          if (!phoneRegex.hasMatch(value)) {
            return 'رقم الهاتف غير صحيح';
          }
          if (value.replaceAll(RegExp(r'[^0-9]'), '').length < 8) {
            return 'رقم الهاتف قصير جداً';
          }
        }
        return null;
      },
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildAddressField() {
    return TextFormField(
      controller: _addressController,
      decoration: InputDecoration(
        labelText: 'العنوان',
        hintText: 'أدخل العنوان',
        prefixIcon: const Icon(Icons.location_on),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      maxLines: 3,
      textInputAction: TextInputAction.done,
    );
  }

  Widget _buildSubmitButton() {
    return ElevatedButton(
      onPressed: widget.isLoading ? null : _handleSubmit,
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorConstants.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child:
          widget.isLoading
              ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
              : Text(
                widget.parent != null ? 'تحديث' : 'إضافة',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
    );
  }

  void _handleSubmit() {
    if (_formKey.currentState!.validate()) {
      final formData = {
        'name': _nameController.text.trim(),
        'email':
            _emailController.text.trim().isEmpty
                ? null
                : _emailController.text.trim(),
        'phone':
            _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
        'address':
            _addressController.text.trim().isEmpty
                ? null
                : _addressController.text.trim(),
      };

      widget.onSubmit(formData);
    }
  }
}
