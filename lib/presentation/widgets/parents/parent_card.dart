import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/parent.dart';

/// Parent card widget for displaying parent information
/// Following Clean Architecture principles
class ParentCard extends StatelessWidget {
  final Parent parent;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onViewDetails;

  const ParentCard({
    super.key,
    required this.parent,
    this.onEdit,
    this.onDelete,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onViewDetails,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: ColorConstants.primary.withOpacity(0.1),
                    backgroundImage: parent.logoPath != null 
                        ? NetworkImage(parent.logoPath!)
                        : null,
                    child: parent.logoPath == null
                        ? Icon(
                            Icons.person,
                            color: ColorConstants.primary,
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          parent.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (parent.email != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            parent.email!,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildActionButtons(),
                ],
              ),
              if (parent.phone != null || parent.address != null) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),
                if (parent.phone != null)
                  _buildInfoRow(Icons.phone, parent.phone!),
                if (parent.address != null)
                  _buildInfoRow(Icons.location_on, parent.address!),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onViewDetails,
          icon: const Icon(Icons.visibility),
          tooltip: 'عرض التفاصيل',
          iconSize: 20,
        ),
        IconButton(
          onPressed: onEdit,
          icon: const Icon(Icons.edit),
          tooltip: 'تعديل',
          iconSize: 20,
        ),
        IconButton(
          onPressed: onDelete,
          icon: const Icon(Icons.delete),
          tooltip: 'حذف',
          iconSize: 20,
          color: Colors.red,
        ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
