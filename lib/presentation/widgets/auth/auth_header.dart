import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/constants/color_constants.dart';

/// AuthHeader widget for authentication pages
/// Following Single Responsibility Principle by focusing only on auth header UI
class AuthHeader extends StatelessWidget {
  final String title;
  final String subtitle;

  const AuthHeader({super.key, required this.title, required this.subtitle});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Compact Logo with smaller size
        Hero(
          tag: 'app_logo',
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  ColorConstants.primary,
                  ColorConstants.primary.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: ColorConstants.primary.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: const Icon(Icons.school, size: 30, color: Colors.white),
          ),
        ),
        const SizedBox(height: 16),

        // Compact app name
        Text(
          AppConstants.appName,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ColorConstants.primary,
            letterSpacing: 0.3,
          ),
        ),
        const SizedBox(height: 12),

        // Smaller title
        Text(
          title,
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: ColorConstants.text,
            height: 1.2,
          ),
        ),
        const SizedBox(height: 6),

        // Compact subtitle
        Text(
          subtitle,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            color: ColorConstants.textSecondary,
            height: 1.3,
          ),
        ),
      ],
    );
  }
}
