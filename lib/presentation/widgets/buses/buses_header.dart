import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/buses_controller.dart';
import '../../routes/app_routes.dart';
import '../accessibility/accessible_button.dart';

/// Buses header widget
/// Following Single Responsibility Principle by focusing only on header UI
class BusesHeader extends StatelessWidget {
  final BusesController controller;

  const BusesHeader({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with title and add button
          _buildHeaderRow(context, isDesktop, isTablet),
          const SizedBox(height: 24),

          // Search and filters row
          _buildSearchRow(context, isDesktop, isTablet),

          // Stats row
          const SizedBox(height: 20),
          _buildStatsRow(context, isDesktop),
        ],
      ),
    );
  }

  /// Build header row with title and add button
  Widget _buildHeaderRow(BuildContext context, bool isDesktop, bool isTablet) {
    if (isDesktop || isTablet) {
      return Row(
        children: [
          // Title and subtitle
          Expanded(child: _buildTitleSection(isDesktop)),

          // Add bus button
          _buildAddBusButton(isDesktop),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and subtitle
          _buildTitleSection(isDesktop),
          const SizedBox(height: 16),

          // Add bus button
          _buildAddBusButton(isDesktop),
        ],
      );
    }
  }

  /// Build title section
  Widget _buildTitleSection(bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorConstants.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.directions_bus_rounded,
                color: ColorConstants.primary,
                size: isDesktop ? 28 : 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bus Management',
                    style: TextStyle(
                      fontSize: isDesktop ? 28 : 24,
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Manage school buses, routes, and transportation',
                    style: TextStyle(
                      fontSize: isDesktop ? 16 : 14,
                      color: ColorConstants.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build add bus button
  Widget _buildAddBusButton(bool isDesktop) {
    return AccessibleButton(
      text: 'Add Bus',
      icon: Icons.add_rounded,
      onPressed: () => Get.toNamed(AppRoutes.addBus),
      backgroundColor: ColorConstants.primary,
      textColor: Colors.white,
      semanticLabel: 'Add new bus',
      semanticHint: 'Navigate to add bus form',
    );
  }

  /// Build search row
  Widget _buildSearchRow(BuildContext context, bool isDesktop, bool isTablet) {
    if (isDesktop) {
      return Row(
        children: [
          // Search field
          Expanded(flex: 2, child: _buildSearchField()),
          const SizedBox(width: 16),

          // Refresh button
          _buildRefreshButton(),
        ],
      );
    } else {
      return Column(
        children: [
          // Search field (full width)
          _buildSearchField(),
          const SizedBox(height: 12),

          // Refresh button (full width)
          SizedBox(width: double.infinity, child: _buildRefreshButton()),
        ],
      );
    }
  }

  /// Build search field
  Widget _buildSearchField() {
    return TextField(
      onChanged: (value) => controller.searchBuses(value),
      decoration: InputDecoration(
        hintText: 'Search buses by name, car number, or notes...',
        prefixIcon: Icon(
          Icons.search_rounded,
          color: ColorConstants.textSecondary,
        ),
        suffixIcon: Obx(
          () =>
              controller.searchQuery.isNotEmpty
                  ? IconButton(
                    icon: Icon(
                      Icons.clear_rounded,
                      color: ColorConstants.textSecondary,
                    ),
                    onPressed: () => controller.searchBuses(''),
                  )
                  : const SizedBox(),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: ColorConstants.primary),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
    );
  }

  /// Build refresh button
  Widget _buildRefreshButton() {
    return Obx(
      () => OutlinedButton.icon(
        onPressed:
            controller.isLoading ? null : () => controller.refreshBuses(),
        icon:
            controller.isLoading
                ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      ColorConstants.primary,
                    ),
                  ),
                )
                : const Icon(Icons.refresh_rounded),
        label: Text(controller.isLoading ? 'Refreshing...' : 'Refresh'),
        style: OutlinedButton.styleFrom(
          foregroundColor: ColorConstants.primary,
          side: BorderSide(color: ColorConstants.primary),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// Build stats row
  Widget _buildStatsRow(BuildContext context, bool isDesktop) {
    return Obx(
      () => Row(
        children: [
          // Total buses stat
          _buildStatCard(
            icon: Icons.directions_bus_rounded,
            label: 'Total Buses',
            value: controller.total.toString(),
            color: ColorConstants.primary,
            isDesktop: isDesktop,
          ),
          const SizedBox(width: 16),

          // Active buses stat (mock data)
          _buildStatCard(
            icon: Icons.check_circle_rounded,
            label: 'Active',
            value: (controller.total * 0.8).round().toString(),
            color: Colors.green,
            isDesktop: isDesktop,
          ),
          const SizedBox(width: 16),

          // Maintenance stat (mock data)
          _buildStatCard(
            icon: Icons.build_rounded,
            label: 'Maintenance',
            value: (controller.total * 0.2).round().toString(),
            color: Colors.orange,
            isDesktop: isDesktop,
          ),
        ],
      ),
    );
  }

  /// Build stat card
  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required bool isDesktop,
  }) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(isDesktop ? 16 : 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: isDesktop ? 24 : 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: isDesktop ? 20 : 18,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: isDesktop ? 14 : 12,
                      color: ColorConstants.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
