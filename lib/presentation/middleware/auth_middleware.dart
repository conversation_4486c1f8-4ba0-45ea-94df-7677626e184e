import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';
import '../routes/app_routes.dart';

/// AuthMiddleware class for handling authentication checks
/// Following Single Responsibility Principle by focusing only on authentication middleware
class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    // Get SharedPreferences instance
    final SharedPreferences prefs = Get.find<SharedPreferences>();

    // Check if user is logged in - check both token locations
    final String? newToken = prefs.getString(AppConstants.tokenKey);
    final String? legacyToken = prefs.getString("token");
    final bool isLoggedIn =
        (newToken?.isNotEmpty ?? false) || (legacyToken?.isNotEmpty ?? false);

    // If token is only in legacy location, migrate it
    if ((newToken == null || newToken.isEmpty) &&
        (legacyToken != null && legacyToken.isNotEmpty)) {
      prefs.setString(AppConstants.tokenKey, legacyToken);
    }

    // Protected routes that require authentication
    final List<String> protectedRoutes = [
      AppRoutes.homePage,
      AppRoutes.home,
      AppRoutes.profile,
      AppRoutes.editProfile,
      AppRoutes.changePassword,
      AppRoutes.students,
      AppRoutes.addStudent,
      AppRoutes.editStudent,
      AppRoutes.parents,
      AppRoutes.addParent,
      AppRoutes.editParent,
      AppRoutes.parentDetails,
      AppRoutes.buses,
      AppRoutes.addBus,
      AppRoutes.editBus,
      AppRoutes.drivers,
      AppRoutes.addDriver,
      AppRoutes.editDriver,
      AppRoutes.driverDetails,
      AppRoutes.supervisors,
      AppRoutes.addSupervisor,
      AppRoutes.editSupervisor,
      AppRoutes.supervisorDetails,
      AppRoutes.trips,
      AppRoutes.currentTrips,
      AppRoutes.previousTrips,
      AppRoutes.previousTripDetails,
      AppRoutes.currentTripDetails,
      AppRoutes.createTrip,
      AppRoutes.trackTrip,
      AppRoutes.attendance,
      AppRoutes.addressChangeRequests,
      AppRoutes.settings,
    ];

    // Check if current route is protected
    if (protectedRoutes.contains(route)) {
      if (!isLoggedIn) {
        // Redirect to login if not authenticated
        return const RouteSettings(name: AppRoutes.login);
      }
    }

    // Auth routes that should redirect to home if already logged in
    final List<String> authRoutes = [
      AppRoutes.login,
      AppRoutes.register,
      AppRoutes.forgotPassword,
      AppRoutes.resetPassword,
    ];

    if (authRoutes.contains(route) && isLoggedIn) {
      // Redirect to home if already authenticated
      return const RouteSettings(name: AppRoutes.homePage);
    }

    // No redirect needed
    return null;
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    // Log navigation for debugging
    debugPrint('🔄 Navigating to: ${page?.name}');
    return super.onPageCalled(page);
  }

  @override
  List<Bindings>? onBindingsStart(List<Bindings>? bindings) {
    // Log bindings initialization
    debugPrint('🔗 Initializing bindings for route');
    return super.onBindingsStart(bindings);
  }

  @override
  GetPageBuilder? onPageBuildStart(GetPageBuilder? page) {
    // Log page build start
    debugPrint('🏗️ Building page');
    return super.onPageBuildStart(page);
  }
}
