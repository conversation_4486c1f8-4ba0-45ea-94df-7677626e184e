import 'package:get/get.dart';
import '../controllers/trip_controller.dart';
import '../controllers/create_trip_controller.dart';

/// TripBinding for dependency injection
/// Following Single Responsibility Principle by focusing only on trip dependencies
class TripBinding extends Bindings {
  @override
  void dependencies() {
    // Trip controller
    Get.lazyPut<TripController>(
      () => TripController(
        getCurrentTripUseCase: Get.find(),
        getRecentTripsUseCase: Get.find(),
        getTripByIdUseCase: Get.find(),
        createTripUseCase: Get.find(),
        updateTripUseCase: Get.find(),
        deleteTripUseCase: Get.find(),
        startTripUseCase: Get.find(),
        endTripUseCase: Get.find(),
        getTripTrackingUseCase: Get.find(),
        getTripsUseCase: Get.find(),
      ),
      fenix: true,
    );

    // Create trip controller
    Get.lazyPut<CreateTripController>(
      () => CreateTripController(
        createTripUseCase: Get.find(),
        getBusesUseCase: Get.find(),
        getDriversUseCase: Get.find(),
        getSupervisorsUseCase: Get.find(),
        getStudentsUseCase: Get.find(),
      ),
      fenix: true,
    );
  }
}
