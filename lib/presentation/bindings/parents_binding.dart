import 'package:get/get.dart';
import '../../data/repositories/parent_repository_impl.dart';
import '../../domain/repositories/parent_repository.dart';
import '../../domain/usecases/create_parent_usecase.dart';
import '../../domain/usecases/delete_parent_usecase.dart';
import '../../domain/usecases/get_parent_by_id_usecase.dart';
import '../../domain/usecases/get_parents_usecase.dart';
import '../../domain/usecases/get_students_by_parent_id_usecase.dart';
import '../../domain/usecases/update_parent_usecase.dart';
import '../controllers/parents_controller.dart';

/// Parents binding for dependency injection
/// Following Clean Architecture and GetX patterns
class ParentsBinding extends Bindings {
  @override
  void dependencies() {
    // Repository
    Get.lazyPut<ParentRepository>(
      () => ParentRepositoryImpl(Get.find()),
    );

    // Use cases
    Get.lazyPut(() => GetParentsUseCase(Get.find()));
    Get.lazyPut(() => GetParentByIdUseCase(Get.find()));
    Get.lazyPut(() => CreateParentUseCase(Get.find()));
    Get.lazyPut(() => UpdateParentUseCase(Get.find()));
    Get.lazyPut(() => DeleteParentUseCase(Get.find()));
    Get.lazyPut(() => GetStudentsByParentIdUseCase(Get.find()));

    // Controller
    Get.lazyPut(
      () => ParentsController(
        Get.find(),
        Get.find(),
        Get.find(),
        Get.find(),
        Get.find(),
        Get.find(),
      ),
    );
  }
}
