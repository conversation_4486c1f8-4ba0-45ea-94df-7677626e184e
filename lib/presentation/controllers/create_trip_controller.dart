import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/bus.dart';
import '../../domain/entities/driver.dart';
import '../../domain/entities/supervisor.dart';
import '../../domain/entities/student.dart';
import '../../domain/entities/trip.dart';
import '../../domain/usecases/trip/create_trip_usecase.dart';
import '../../domain/usecases/get_buses_usecase.dart';
import '../../domain/usecases/get_drivers_usecase.dart';
import '../../domain/usecases/get_supervisors_usecase.dart';
import '../../domain/usecases/get_students_usecase.dart';

/// Controller for creating new trips
/// Following Single Responsibility Principle by focusing only on trip creation
class CreateTripController extends GetxController {
  final CreateTripUseCase createTripUseCase;
  final GetBusesUseCase getBusesUseCase;
  final GetDriversUseCase getDriversUseCase;
  final GetSupervisorsUseCase getSupervisorsUseCase;
  final GetStudentsUseCase getStudentsUseCase;

  CreateTripController({
    required this.createTripUseCase,
    required this.getBusesUseCase,
    required this.getDriversUseCase,
    required this.getSupervisorsUseCase,
    required this.getStudentsUseCase,
  });

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final tripNameController = TextEditingController();
  final startLocationController = TextEditingController();
  final endLocationController = TextEditingController();

  // Observable variables
  final _isLoading = false.obs;
  final _isCreating = false.obs;
  final _errorMessage = ''.obs;

  // Data lists
  final _buses = <Bus>[].obs;
  final _drivers = <Driver>[].obs;
  final _supervisors = <Supervisor>[].obs;
  final _students = <Student>[].obs;

  // Selected values
  final _selectedBus = Rxn<Bus>();
  final _selectedDriver = Rxn<Driver>();
  final _selectedSupervisor = Rxn<Supervisor>();
  final _selectedStudents = <Student>[].obs;
  final _selectedStartTime = Rxn<DateTime>();
  final _selectedTripType = 'morning'.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isCreating => _isCreating.value;
  String get errorMessage => _errorMessage.value;
  List<Bus> get buses => _buses;
  List<Driver> get drivers => _drivers;
  List<Supervisor> get supervisors => _supervisors;
  List<Student> get students => _students;
  Bus? get selectedBus => _selectedBus.value;
  Driver? get selectedDriver => _selectedDriver.value;
  Supervisor? get selectedSupervisor => _selectedSupervisor.value;
  List<Student> get selectedStudents => _selectedStudents;
  DateTime? get selectedStartTime => _selectedStartTime.value;
  String get selectedTripType => _selectedTripType.value;

  @override
  void onInit() {
    super.onInit();
    _loadInitialData();
  }

  @override
  void onClose() {
    tripNameController.dispose();
    startLocationController.dispose();
    endLocationController.dispose();
    super.onClose();
  }

  /// Load initial data for dropdowns
  Future<void> _loadInitialData() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Loading initial data for trip creation');

      // Load all required data in parallel
      await Future.wait([
        _loadBuses(),
        _loadDrivers(),
        _loadSupervisors(),
        _loadStudents(),
      ]);

      LoggerService.success('Initial data loaded successfully');
    } catch (e) {
      LoggerService.error('Failed to load initial data', error: e);
      _errorMessage.value = 'فشل في تحميل البيانات الأولية';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load buses
  Future<void> _loadBuses() async {
    try {
      final result = await getBusesUseCase.call(perPage: 100); // Get all buses
      result.fold(
        (failure) {
          LoggerService.error('Failed to load buses', error: failure.message);
          throw Exception(failure.message);
        },
        (buses) {
          _buses.assignAll(buses);
          LoggerService.debug('Loaded ${buses.length} buses');
        },
      );
    } catch (e) {
      LoggerService.error('Error loading buses', error: e);
      rethrow;
    }
  }

  /// Load drivers
  Future<void> _loadDrivers() async {
    try {
      final result = await getDriversUseCase.call(GetDriversParams());
      result.fold(
        (failure) {
          LoggerService.error('Failed to load drivers', error: failure.message);
          throw Exception(failure.message);
        },
        (drivers) {
          _drivers.assignAll(drivers);
          LoggerService.debug('Loaded ${drivers.length} drivers');
        },
      );
    } catch (e) {
      LoggerService.error('Error loading drivers', error: e);
      rethrow;
    }
  }

  /// Load supervisors
  Future<void> _loadSupervisors() async {
    try {
      final result = await getSupervisorsUseCase.call(GetSupervisorsParams());
      result.fold(
        (failure) {
          LoggerService.error(
            'Failed to load supervisors',
            error: failure.message,
          );
          throw Exception(failure.message);
        },
        (supervisors) {
          _supervisors.assignAll(supervisors);
          LoggerService.debug('Loaded ${supervisors.length} supervisors');
        },
      );
    } catch (e) {
      LoggerService.error('Error loading supervisors', error: e);
      rethrow;
    }
  }

  /// Load students
  Future<void> _loadStudents() async {
    try {
      final result = await getStudentsUseCase.call(GetStudentsParams());
      result.fold(
        (failure) {
          LoggerService.error(
            'Failed to load students',
            error: failure.message,
          );
          throw Exception(failure.message);
        },
        (students) {
          _students.assignAll(students);
          LoggerService.debug('Loaded ${students.length} students');
        },
      );
    } catch (e) {
      LoggerService.error('Error loading students', error: e);
      rethrow;
    }
  }

  /// Set selected bus
  void setSelectedBus(Bus? bus) {
    _selectedBus.value = bus;
    LoggerService.debug(
      'Selected bus',
      data: {'busId': bus?.id, 'busName': bus?.name},
    );
  }

  /// Set selected driver
  void setSelectedDriver(Driver? driver) {
    _selectedDriver.value = driver;
    LoggerService.debug(
      'Selected driver',
      data: {'driverId': driver?.id, 'driverName': driver?.name},
    );
  }

  /// Set selected supervisor
  void setSelectedSupervisor(Supervisor? supervisor) {
    _selectedSupervisor.value = supervisor;
    LoggerService.debug(
      'Selected supervisor',
      data: {
        'supervisorId': supervisor?.id,
        'supervisorName': supervisor?.name,
      },
    );
  }

  /// Set selected start time
  void setSelectedStartTime(DateTime? time) {
    _selectedStartTime.value = time;
    LoggerService.debug(
      'Selected start time',
      data: {'startTime': time?.toIso8601String()},
    );
  }

  /// Set trip type
  void setTripType(String type) {
    _selectedTripType.value = type;
    LoggerService.debug('Selected trip type', data: {'tripType': type});
  }

  /// Toggle student selection
  void toggleStudentSelection(Student student) {
    if (_selectedStudents.contains(student)) {
      _selectedStudents.remove(student);
    } else {
      _selectedStudents.add(student);
    }
    LoggerService.debug(
      'Student selection toggled',
      data: {
        'studentId': student.id,
        'studentName': student.name,
        'isSelected': _selectedStudents.contains(student),
        'totalSelected': _selectedStudents.length,
      },
    );
  }

  /// Create trip
  Future<void> createTrip() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (!_validateSelections()) {
      return;
    }

    try {
      _isCreating.value = true;
      _errorMessage.value = '';

      LoggerService.info('Creating new trip');

      // Create trip entity
      final trip = Trip(
        id: '', // Will be assigned by server
        name: tripNameController.text.trim(),
        schoolId: '1', // TODO: Get from user session
        schoolName: 'مدرسة تجريبية', // TODO: Get from user session
        busId: selectedBus!.id.toString(),
        busNumber: selectedBus!.carNumber ?? '',
        driverId: selectedDriver!.id.toString(),
        driverName: selectedDriver!.name ?? '',
        supervisorId: selectedSupervisor!.id.toString(),
        supervisorName: selectedSupervisor!.name ?? '',
        startTime: selectedStartTime ?? DateTime.now(),
        status: 'scheduled',
        studentIds: selectedStudents.map((s) => s.id.toString()).toList(),
        attendedStudentIds: [],
        startLocation: startLocationController.text.trim(),
        endLocation: endLocationController.text.trim(),
        distance: 0.0,
        duration: 0,
        stops: [],
        events: [],
      );

      final result = await createTripUseCase.call(trip);

      result.fold(
        (failure) {
          LoggerService.error('Failed to create trip', error: failure.message);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
        (createdTrip) {
          LoggerService.success('Trip created successfully');
          Get.snackbar(
            'نجح',
            'تم إنشاء الرحلة بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          Get.back(result: createdTrip);
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error creating trip', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isCreating.value = false;
    }
  }

  /// Validate selections
  bool _validateSelections() {
    if (selectedBus == null) {
      _errorMessage.value = 'يرجى اختيار الحافلة';
      return false;
    }

    if (selectedDriver == null) {
      _errorMessage.value = 'يرجى اختيار السائق';
      return false;
    }

    if (selectedSupervisor == null) {
      _errorMessage.value = 'يرجى اختيار المشرف';
      return false;
    }

    if (selectedStartTime == null) {
      _errorMessage.value = 'يرجى اختيار وقت البداية';
      return false;
    }

    if (selectedStudents.isEmpty) {
      _errorMessage.value = 'يرجى اختيار الطلاب';
      return false;
    }

    return true;
  }
}
