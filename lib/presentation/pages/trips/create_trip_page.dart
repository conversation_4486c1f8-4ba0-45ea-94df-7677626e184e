import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/create_trip_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_dropdown.dart';
import '../../widgets/trips/student_selection_widget.dart';

/// Create trip page
/// Following Single Responsibility Principle by focusing only on trip creation UI
class CreateTripPage extends StatelessWidget {
  const CreateTripPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreateTripController>();

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(
        child: Obx(() {
          if (controller.isLoading) {
            return const Center(
              child: LoadingWidget(message: 'جاري تحميل البيانات...'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: controller.formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPageHeader(context),
                  const SizedBox(height: 24),
                  _buildTripForm(context, controller),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildPageHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withAlpha(77),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.add_road_rounded,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إنشاء رحلة جديدة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'إنشاء رحلة جديدة للطلاب مع تحديد الحافلة والسائق والمشرف',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(230),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close_rounded, color: Colors.white),
            tooltip: 'إغلاق',
          ),
        ],
      ),
    );
  }

  Widget _buildTripForm(BuildContext context, CreateTripController controller) {
    return ResponsiveUtils.isMobile(context)
        ? _buildMobileLayout(controller)
        : _buildDesktopLayout(controller);
  }

  Widget _buildMobileLayout(CreateTripController controller) {
    return Column(
      children: [
        _buildBasicInfoSection(controller),
        const SizedBox(height: 24),
        _buildSelectionSection(controller),
        const SizedBox(height: 24),
        _buildStudentSelectionSection(controller),
        const SizedBox(height: 24),
        _buildActionButtons(controller),
      ],
    );
  }

  Widget _buildDesktopLayout(CreateTripController controller) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column
        Expanded(
          flex: 2,
          child: Column(
            children: [
              _buildBasicInfoSection(controller),
              const SizedBox(height: 24),
              _buildSelectionSection(controller),
            ],
          ),
        ),
        const SizedBox(width: 24),
        // Right column
        Expanded(
          flex: 3,
          child: Column(
            children: [
              _buildStudentSelectionSection(controller),
              const SizedBox(height: 24),
              _buildActionButtons(controller),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection(CreateTripController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline_rounded,
                  color: Get.theme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'المعلومات الأساسية',
                  style: Get.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            CustomTextField(
              controller: controller.tripNameController,
              labelText: 'اسم الرحلة',
              hintText: 'أدخل اسم الرحلة',
              prefixIcon: Icons.directions_bus_rounded,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم الرحلة مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: controller.startLocationController,
              labelText: 'موقع البداية',
              hintText: 'أدخل موقع بداية الرحلة',
              prefixIcon: Icons.location_on_rounded,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'موقع البداية مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: controller.endLocationController,
              labelText: 'موقع النهاية',
              hintText: 'أدخل موقع نهاية الرحلة',
              prefixIcon: Icons.flag_rounded,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'موقع النهاية مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildTripTypeSelector(controller),
            const SizedBox(height: 16),
            _buildTimeSelector(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildTripTypeSelector(CreateTripController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الرحلة',
          style: Get.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Obx(
          () => Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('صباحية'),
                  value: 'morning',
                  groupValue: controller.selectedTripType,
                  onChanged: (value) => controller.setTripType(value!),
                  dense: true,
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('مسائية'),
                  value: 'evening',
                  groupValue: controller.selectedTripType,
                  onChanged: (value) => controller.setTripType(value!),
                  dense: true,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSelector(CreateTripController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'وقت البداية',
          style: Get.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Obx(
          () => InkWell(
            onTap: () => _selectTime(controller),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.access_time_rounded, color: Colors.grey[600]),
                  const SizedBox(width: 12),
                  Text(
                    controller.selectedStartTime != null
                        ? '${controller.selectedStartTime!.hour.toString().padLeft(2, '0')}:${controller.selectedStartTime!.minute.toString().padLeft(2, '0')}'
                        : 'اختر وقت البداية',
                    style: TextStyle(
                      color:
                          controller.selectedStartTime != null
                              ? Colors.black87
                              : Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  Icon(Icons.arrow_drop_down, color: Colors.grey[600]),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectionSection(CreateTripController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.assignment_ind_rounded,
                  color: Get.theme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'اختيار الموارد',
                  style: Get.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Obx(
              () => CustomDropdown<int>(
                label: 'الحافلة',
                hint: 'اختر الحافلة',
                value: controller.selectedBus?.id,
                items:
                    controller.buses
                        .map(
                          (bus) => DropdownMenuItem<int>(
                            value: bus.id,
                            child: Text('${bus.name} - ${bus.carNumber}'),
                          ),
                        )
                        .toList(),
                onChanged: (value) {
                  final bus = controller.buses.firstWhere((b) => b.id == value);
                  controller.setSelectedBus(bus);
                },
                prefixIcon: Icons.directions_bus_rounded,
              ),
            ),
            const SizedBox(height: 16),
            Obx(
              () => CustomDropdown<int>(
                label: 'السائق',
                hint: 'اختر السائق',
                value: controller.selectedDriver?.id,
                items:
                    controller.drivers
                        .map(
                          (driver) => DropdownMenuItem<int>(
                            value: driver.id,
                            child: Text(driver.name ?? ''),
                          ),
                        )
                        .toList(),
                onChanged: (value) {
                  final driver = controller.drivers.firstWhere(
                    (d) => d.id == value,
                  );
                  controller.setSelectedDriver(driver);
                },
                prefixIcon: Icons.person_rounded,
              ),
            ),
            const SizedBox(height: 16),
            Obx(
              () => CustomDropdown<int>(
                label: 'المشرف',
                hint: 'اختر المشرف',
                value: controller.selectedSupervisor?.id,
                items:
                    controller.supervisors
                        .map(
                          (supervisor) => DropdownMenuItem<int>(
                            value: supervisor.id,
                            child: Text(supervisor.name ?? ''),
                          ),
                        )
                        .toList(),
                onChanged: (value) {
                  final supervisor = controller.supervisors.firstWhere(
                    (s) => s.id == value,
                  );
                  controller.setSelectedSupervisor(supervisor);
                },
                prefixIcon: Icons.supervisor_account_rounded,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentSelectionSection(CreateTripController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people_alt_rounded,
                  color: Get.theme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'اختيار الطلاب',
                  style: Get.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Obx(
                  () => Chip(
                    label: Text('${controller.selectedStudents.length} طالب'),
                    backgroundColor: ColorConstants.primary.withAlpha(51),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            StudentSelectionWidget(
              students: controller.students,
              selectedStudents: controller.selectedStudents,
              onStudentToggle: controller.toggleStudentSelection,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(CreateTripController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Obx(() {
              if (controller.errorMessage.isNotEmpty) {
                return Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red[700]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          controller.errorMessage,
                          style: TextStyle(color: Colors.red[700]),
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Get.back(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(
                    () => ElevatedButton(
                      onPressed:
                          controller.isCreating ? null : controller.createTrip,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.primary,
                        foregroundColor: Colors.white,
                      ),
                      child:
                          controller.isCreating
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : const Text('إنشاء الرحلة'),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectTime(CreateTripController controller) async {
    final TimeOfDay? picked = await showTimePicker(
      context: Get.context!,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      final now = DateTime.now();
      final selectedDateTime = DateTime(
        now.year,
        now.month,
        now.day,
        picked.hour,
        picked.minute,
      );
      controller.setSelectedStartTime(selectedDateTime);
    }
  }
}
