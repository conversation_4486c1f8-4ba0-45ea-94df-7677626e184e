import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/parent.dart';
import '../../controllers/parents_controller.dart';
import '../../widgets/parents/parent_form.dart';

/// Add parent page
/// Following Clean Architecture and GetX patterns
class AddParentPage extends GetView<ParentsController> {
  const AddParentPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة ولي أمر'),
        backgroundColor: ColorConstants.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: ParentForm(
          onSubmit: _handleSubmit,
          isLoading: controller.isLoading,
        ),
      ),
    );
  }

  Future<void> _handleSubmit(Map<String, dynamic> formData) async {
    final parent = Parent(
      name: formData['name'],
      email: formData['email'],
      phone: formData['phone'],
      address: formData['address'],
      createdAt: DateTime.now().toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
    );

    final success = await controller.createParent(parent);

    if (success) {
      Get.back();
      Get.snackbar(
        'نجح',
        'تم إضافة ولي الأمر بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        'خطأ',
        controller.error.isNotEmpty
            ? controller.error
            : 'فشل في إضافة ولي الأمر',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
