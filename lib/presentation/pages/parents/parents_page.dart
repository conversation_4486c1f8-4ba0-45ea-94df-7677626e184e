import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../controllers/parents_controller.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/parents/parent_card.dart';
import '../../routes/app_routes.dart';

/// Parents page for displaying list of parents
/// Following Clean Architecture and GetX patterns
class ParentsPage extends GetView<ParentsController> {
  const ParentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أولياء الأمور'),
        backgroundColor: ColorConstants.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => Get.toNamed(AppRoutes.addParent),
            icon: const Icon(Icons.add),
            tooltip: 'إضافة ولي أمر',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: Obx(() => _buildContent()),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        onChanged: controller.searchParents,
        decoration: InputDecoration(
          hintText: 'البحث عن ولي أمر...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Colors.grey[100],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (controller.isLoading && controller.parents.isEmpty) {
      return const LoadingWidget();
    }

    if (controller.error.isNotEmpty && controller.parents.isEmpty) {
      return CustomErrorWidget(
        message: controller.error,
        onRetry: () => controller.loadParents(refresh: true),
      );
    }

    if (controller.parents.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.family_restroom_rounded,
        title: 'لا يوجد أولياء أمور',
        subtitle: 'لم يتم العثور على أي أولياء أمور',
        actionText: 'إضافة ولي أمر',
        onAction: () => Get.toNamed(AppRoutes.addParent),
      );
    }

    return RefreshIndicator(
      onRefresh: () => controller.loadParents(refresh: true),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: controller.parents.length + (controller.hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == controller.parents.length) {
            // Load more indicator
            if (controller.hasMoreData) {
              controller.loadMoreParents();
              return const Padding(
                padding: EdgeInsets.all(16),
                child: Center(child: CircularProgressIndicator()),
              );
            }
            return const SizedBox.shrink();
          }

          final parent = controller.parents[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: ParentCard(
              parent: parent,
              onEdit: () => Get.toNamed(
                AppRoutes.editParent,
                arguments: parent,
              ),
              onDelete: () => _showDeleteDialog(parent),
              onViewDetails: () => Get.toNamed(
                AppRoutes.parentDetails,
                arguments: parent,
              ),
            ),
          );
        },
      ),
    );
  }

  void _showDeleteDialog(parent) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف ولي الأمر "${parent.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success = await controller.deleteParent(parent.id);
              if (success) {
                Get.snackbar(
                  'نجح',
                  'تم حذف ولي الأمر بنجاح',
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  controller.error.isNotEmpty 
                      ? controller.error 
                      : 'فشل في حذف ولي الأمر',
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
