import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../routes/app_routes.dart';
import '../../widgets/common/custom_button.dart';

/// NotFoundPage - 404 Error Page
/// Following Single Responsibility Principle by focusing only on 404 error display
class NotFoundPage extends StatelessWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(isDesktop ? 48 : 24),
            child: Container(
              constraints: BoxConstraints(
                maxWidth: isDesktop ? 600 : double.infinity,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 404 Illustration
                  _build404Illustration(context, isDesktop),
                  
                  SizedBox(height: isDesktop ? 48 : 32),
                  
                  // Error Title
                  _buildErrorTitle(context, isDesktop),
                  
                  SizedBox(height: isDesktop ? 24 : 16),
                  
                  // Error Description
                  _buildErrorDescription(context, isDesktop),
                  
                  SizedBox(height: isDesktop ? 48 : 32),
                  
                  // Action Buttons
                  _buildActionButtons(context, isDesktop, isTablet),
                  
                  SizedBox(height: isDesktop ? 32 : 24),
                  
                  // Helpful Links
                  _buildHelpfulLinks(context, isDesktop),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build 404 illustration
  Widget _build404Illustration(BuildContext context, bool isDesktop) {
    return Container(
      width: isDesktop ? 300 : 200,
      height: isDesktop ? 300 : 200,
      decoration: BoxDecoration(
        color: ColorConstants.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(isDesktop ? 150 : 100),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: isDesktop ? 80 : 60,
              color: ColorConstants.primary,
            ),
            const SizedBox(height: 16),
            Text(
              '404',
              style: TextStyle(
                fontSize: isDesktop ? 48 : 36,
                fontWeight: FontWeight.bold,
                color: ColorConstants.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build error title
  Widget _buildErrorTitle(BuildContext context, bool isDesktop) {
    return Text(
      'pageNotFound'.tr,
      style: TextStyle(
        fontSize: isDesktop ? 32 : 24,
        fontWeight: FontWeight.bold,
        color: ColorConstants.text,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// Build error description
  Widget _buildErrorDescription(BuildContext context, bool isDesktop) {
    return Text(
      'pageNotFoundDescription'.tr,
      style: TextStyle(
        fontSize: isDesktop ? 18 : 16,
        color: ColorConstants.textSecondary,
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context, bool isDesktop, bool isTablet) {
    if (isDesktop || isTablet) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomButton(
            text: 'goHome'.tr,
            onPressed: () => Get.offAllNamed(AppRoutes.homePage),
            icon: Icons.home_rounded,
            width: 180,
          ),
          const SizedBox(width: 16),
          CustomButton(
            text: 'goBack'.tr,
            onPressed: () => Get.back(),
            icon: Icons.arrow_back_rounded,
            isOutlined: true,
            width: 180,
          ),
        ],
      );
    }

    return Column(
      children: [
        CustomButton(
          text: 'goHome'.tr,
          onPressed: () => Get.offAllNamed(AppRoutes.homePage),
          icon: Icons.home_rounded,
          width: double.infinity,
        ),
        const SizedBox(height: 16),
        CustomButton(
          text: 'goBack'.tr,
          onPressed: () => Get.back(),
          icon: Icons.arrow_back_rounded,
          isOutlined: true,
          width: double.infinity,
        ),
      ],
    );
  }

  /// Build helpful links
  Widget _buildHelpfulLinks(BuildContext context, bool isDesktop) {
    return Column(
      children: [
        Text(
          'quickLinks'.tr,
          style: TextStyle(
            fontSize: isDesktop ? 18 : 16,
            fontWeight: FontWeight.w600,
            color: ColorConstants.text,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 16,
          runSpacing: 8,
          children: [
            _buildQuickLink(
              'students'.tr,
              Icons.school_rounded,
              () => Get.offAllNamed(AppRoutes.students),
            ),
            _buildQuickLink(
              'buses'.tr,
              Icons.directions_bus_rounded,
              () => Get.offAllNamed(AppRoutes.buses),
            ),
            _buildQuickLink(
              'drivers'.tr,
              Icons.person_rounded,
              () => Get.offAllNamed(AppRoutes.drivers),
            ),
            _buildQuickLink(
              'trips'.tr,
              Icons.map_rounded,
              () => Get.offAllNamed(AppRoutes.currentTrips),
            ),
          ],
        ),
      ],
    );
  }

  /// Build quick link
  Widget _buildQuickLink(String title, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: ColorConstants.primary.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: ColorConstants.primary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                color: ColorConstants.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
