import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/supervisors_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/supervisors/supervisor_form.dart';
import '../../widgets/common/loading_widget.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/supervisor.dart';

/// Edit supervisor page with responsive design
/// Following Single Responsibility Principle by focusing only on supervisor editing
class EditSupervisorPage extends StatefulWidget {
  const EditSupervisorPage({super.key});

  @override
  State<EditSupervisorPage> createState() => _EditSupervisorPageState();
}

class _EditSupervisorPageState extends State<EditSupervisorPage> {
  final SupervisorsController _controller = Get.find<SupervisorsController>();
  Supervisor? _supervisor;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSupervisor();
  }

  Future<void> _loadSupervisor() async {
    final supervisor = Get.arguments as Supervisor?;
    if (supervisor != null) {
      setState(() {
        _supervisor = supervisor;
        _isLoading = false;
      });
      // Load form data
      await _controller.loadFormData();
    } else {
      // If no supervisor passed, try to get from route parameters
      final supervisorId = Get.parameters['id'];
      if (supervisorId != null) {
        final fetchedSupervisor = await _controller.getSupervisorById(int.parse(supervisorId));
        if (fetchedSupervisor != null) {
          setState(() {
            _supervisor = fetchedSupervisor;
            _isLoading = false;
          });
          await _controller.loadFormData();
        } else {
          Get.back();
          Get.snackbar(
            'خطأ',
            'لم يتم العثور على المشرف',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Get.back();
        Get.snackbar(
          'خطأ',
          'معرف المشرف مطلوب',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSidebar(
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark 
            ? ColorConstants.backgroundDark 
            : ColorConstants.background,
        appBar: _buildAppBar(context),
        body: _buildBody(context),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'تعديل المشرف',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: ColorConstants.white,
        ),
      ),
      backgroundColor: ColorConstants.primary,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: ColorConstants.white),
        onPressed: () => Get.back(),
      ),
      actions: [
        if (!_isLoading && _supervisor != null)
          IconButton(
            icon: const Icon(Icons.refresh, color: ColorConstants.white),
            onPressed: _loadSupervisor,
            tooltip: 'تحديث البيانات',
          ),
      ],
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: LoadingWidget(message: 'جاري تحميل بيانات المشرف...'),
      );
    }

    if (_supervisor == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لم يتم العثور على المشرف',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Get.back(),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                foregroundColor: ColorConstants.white,
              ),
              child: const Text('العودة'),
            ),
          ],
        ),
      );
    }

    return GetBuilder<SupervisorsController>(
      builder: (controller) {
        return SupervisorForm(
          supervisor: _supervisor,
          availableBuses: controller.availableBuses,
          genderOptions: controller.genderOptions,
          religionOptions: controller.religionOptions,
          onSubmit: _handleSubmit,
          isLoading: controller.isLoading,
        );
      },
    );
  }

  Future<void> _handleSubmit(Supervisor supervisor) async {
    final success = await _controller.updateSupervisor(supervisor);
    if (success) {
      Get.back();
      Get.snackbar(
        'نجح',
        'تم تحديث المشرف بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ColorConstants.success.withAlpha(150),
        colorText: ColorConstants.white,
      );
    }
  }
}
