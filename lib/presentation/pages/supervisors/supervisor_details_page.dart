import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/supervisors_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/supervisor.dart';

/// Supervisor details page with responsive design
/// Following Single Responsibility Principle by focusing only on supervisor details display
class SupervisorDetailsPage extends StatefulWidget {
  const SupervisorDetailsPage({super.key});

  @override
  State<SupervisorDetailsPage> createState() => _SupervisorDetailsPageState();
}

class _SupervisorDetailsPageState extends State<SupervisorDetailsPage> {
  final SupervisorsController _controller = Get.find<SupervisorsController>();
  Supervisor? _supervisor;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSupervisor();
  }

  Future<void> _loadSupervisor() async {
    final supervisor = Get.arguments as Supervisor?;
    if (supervisor != null) {
      setState(() {
        _supervisor = supervisor;
        _isLoading = false;
      });
    } else {
      // If no supervisor passed, try to get from route parameters
      final supervisorId = Get.parameters['id'];
      if (supervisorId != null) {
        final fetchedSupervisor = await _controller.getSupervisorById(
          int.parse(supervisorId),
        );
        if (fetchedSupervisor != null) {
          setState(() {
            _supervisor = fetchedSupervisor;
            _isLoading = false;
          });
        } else {
          Get.back();
          Get.snackbar(
            'خطأ',
            'لم يتم العثور على المشرف',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Get.back();
        Get.snackbar(
          'خطأ',
          'معرف المشرف مطلوب',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSidebar(
      child: Scaffold(
        backgroundColor:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.backgroundDark
                : ColorConstants.background,
        appBar: _buildAppBar(context),
        body: _buildBody(context),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'تفاصيل المشرف',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: ColorConstants.white,
        ),
      ),
      backgroundColor: ColorConstants.primary,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: ColorConstants.white),
        onPressed: () => Get.back(),
      ),
      actions: [
        if (!_isLoading && _supervisor != null) ...[
          IconButton(
            icon: const Icon(Icons.edit, color: ColorConstants.white),
            onPressed:
                () => Get.toNamed(
                  AppRoutes.editSupervisor,
                  arguments: _supervisor,
                ),
            tooltip: 'تعديل المشرف',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: ColorConstants.white),
            onPressed: _loadSupervisor,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ],
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: LoadingWidget(message: 'جاري تحميل بيانات المشرف...'),
      );
    }

    if (_supervisor == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لم يتم العثور على المشرف',
              style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Get.back(),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                foregroundColor: ColorConstants.white,
              ),
              child: const Text('العودة'),
            ),
          ],
        ),
      );
    }

    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSupervisorHeader(),
          const SizedBox(height: 32),
          if (isMobile) ...[
            _buildBasicInfoCard(),
            const SizedBox(height: 16),
            _buildContactInfoCard(),
            const SizedBox(height: 16),
            _buildAdditionalInfoCard(),
            const SizedBox(height: 16),
            _buildBusAssignmentCard(),
          ] else ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      _buildBasicInfoCard(),
                      const SizedBox(height: 16),
                      _buildContactInfoCard(),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      _buildAdditionalInfoCard(),
                      const SizedBox(height: 16),
                      _buildBusAssignmentCard(),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    if (_supervisor == null) return const SizedBox.shrink();

    return FloatingActionButton(
      onPressed:
          () => Get.toNamed(AppRoutes.editSupervisor, arguments: _supervisor),
      backgroundColor: ColorConstants.primary,
      child: const Icon(Icons.edit, color: ColorConstants.white),
    );
  }

  Widget _buildSupervisorHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ColorConstants.primary,
            ColorConstants.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: ColorConstants.white.withValues(alpha: 0.2),
            child: Text(
              _supervisor!.name?.isNotEmpty == true
                  ? _supervisor!.name![0].toUpperCase()
                  : 'M',
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: ColorConstants.white,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _supervisor!.name ?? 'غير محدد',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: ColorConstants.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color:
                  _supervisor!.status == 1
                      ? ColorConstants.success.withValues(alpha: 0.2)
                      : ColorConstants.error.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color:
                    _supervisor!.status == 1
                        ? ColorConstants.success
                        : ColorConstants.error,
                width: 1,
              ),
            ),
            child: Text(
              _supervisor!.status == 1 ? 'نشط' : 'غير نشط',
              style: TextStyle(
                color:
                    _supervisor!.status == 1
                        ? ColorConstants.success
                        : ColorConstants.error,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return _buildInfoCard(
      title: 'المعلومات الأساسية',
      icon: Icons.person,
      children: [
        _buildInfoRow('الاسم الكامل', _supervisor!.name ?? 'غير محدد'),
        _buildInfoRow('اسم المستخدم', _supervisor!.username ?? 'غير محدد'),
        _buildInfoRow('الجنس', _supervisor!.genderName ?? 'غير محدد'),
        _buildInfoRow('الديانة', _supervisor!.religionName ?? 'غير محدد'),
      ],
    );
  }

  Widget _buildContactInfoCard() {
    return _buildInfoCard(
      title: 'معلومات الاتصال',
      icon: Icons.contact_phone,
      children: [
        _buildInfoRow('البريد الإلكتروني', _supervisor!.email ?? 'غير محدد'),
        _buildInfoRow('رقم الهاتف', _supervisor!.phone ?? 'غير محدد'),
        _buildInfoRow('العنوان', _supervisor!.address ?? 'غير محدد'),
        _buildInfoRow('المدينة', _supervisor!.cityName ?? 'غير محدد'),
      ],
    );
  }

  Widget _buildAdditionalInfoCard() {
    return _buildInfoCard(
      title: 'المعلومات الإضافية',
      icon: Icons.info,
      children: [
        _buildInfoRow('تاريخ الميلاد', _formatDate(_supervisor!.birthDate)),
        _buildInfoRow('تاريخ الانضمام', _formatDate(_supervisor!.joiningDate)),
        _buildInfoRow('تاريخ الإنشاء', _formatDate(_supervisor!.createdAt)),
        _buildInfoRow('آخر تحديث', _formatDate(_supervisor!.updatedAt)),
      ],
    );
  }

  Widget _buildBusAssignmentCard() {
    return _buildInfoCard(
      title: 'تخصيص الحافلة',
      icon: Icons.directions_bus,
      children: [
        _buildInfoRow('اسم الحافلة', _supervisor!.busName ?? 'غير مخصص'),
        _buildInfoRow('رقم الحافلة', _supervisor!.busCarNumber ?? 'غير محدد'),
        _buildInfoRow('المدرسة', _supervisor!.schoolName ?? 'غير محدد'),
      ],
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.surfaceDark
                : ColorConstants.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? ColorConstants.textSecondaryDark
                  : ColorConstants.textSecondary,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: ColorConstants.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textPrimaryDark
                          : ColorConstants.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textSecondaryDark
                        : ColorConstants.textSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textPrimaryDark
                        : ColorConstants.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return 'غير محدد';
    }

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
