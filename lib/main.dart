import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'core/constants/app_constants.dart';
import 'core/di/dependency_injection.dart';
import 'core/localization/app_translations.dart';
import 'core/localization/language_binding.dart';
import 'core/localization/language_controller.dart';
import 'core/performance/cache_manager.dart';
import 'core/performance/performance_monitor.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/controller_utils.dart';
import 'presentation/bindings/main_binding.dart';
import 'presentation/bindings/theme_binding.dart';
import 'presentation/controllers/theme_controller.dart';
import 'presentation/routes/app_pages.dart';
import 'presentation/routes/app_routes.dart';

/// Main entry point for the application
/// Following Single Responsibility Principle by focusing only on app initialization
void main() async {
  // Start performance monitoring
  PerformanceMonitor.startTimer('App Initialization');

  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize performance monitoring
  PerformanceMonitor.startFrameRateMonitoring();

  // Initialize cache manager
  await CacheManager.init();

  // Clear expired cache entries
  await CacheManager.instance.clearExpired();

  // Initialize SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  Get.put<SharedPreferences>(sharedPreferences, permanent: true);

  // Initialize dependencies
  await DependencyInjection.init();

  // Initialize main binding
  MainBinding().dependencies();

  // Initialize theme binding
  ThemeBinding().dependencies();

  // Initialize language binding
  LanguageBinding().dependencies();

  // Verify that the LanguageController is properly initialized
  if (!Get.isRegistered<LanguageController>()) {
    Get.put<LanguageController>(
      LanguageController(prefs: Get.find<SharedPreferences>()),
      permanent: true,
    );
  }

  // End performance monitoring
  PerformanceMonitor.endTimer('App Initialization');

  // Run the app
  runApp(const BusatyApp());
}

/// BusatyApp class for the main application
/// Following Single Responsibility Principle by focusing only on app configuration
class BusatyApp extends StatelessWidget {
  const BusatyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the controllers safely using our utility functions
    final ThemeController themeController =
        ControllerUtils.getThemeController();
    final LanguageController languageController =
        ControllerUtils.getLanguageController();

    // Check if it's the first launch
    final SharedPreferences prefs = Get.find<SharedPreferences>();
    final bool isFirstLaunch = prefs.getBool('is_first_launch') ?? true;

    // Check if user is logged in - check both token locations
    final String? newToken = prefs.getString(AppConstants.tokenKey);
    final String? legacyToken = prefs.getString("token");
    final bool isLoggedIn =
        (newToken?.isNotEmpty ?? false) || (legacyToken?.isNotEmpty ?? false);

    // If token is only in legacy location, migrate it
    if ((newToken == null || newToken.isEmpty) &&
        (legacyToken != null && legacyToken.isNotEmpty)) {
      prefs.setString(AppConstants.tokenKey, legacyToken);
    }

    // Set initial route based on first launch and login status
    String initialRoute;
    if (isFirstLaunch) {
      initialRoute = AppRoutes.languageSelection;
    } else if (isLoggedIn) {
      initialRoute = AppRoutes.homePage; // Use the new home page
    } else {
      initialRoute = AppRoutes.login;
    }

    // If it's the first launch, set the flag to false for next time
    if (isFirstLaunch) {
      prefs.setBool('is_first_launch', false);
    }

    return Obx(
      () => GetMaterialApp(
        title: AppConstants.appName,
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeController.themeMode, // Use the theme from controller
        // Localization
        translations: AppTranslations(),
        locale: languageController.locale,
        fallbackLocale: const Locale('en'),

        // Routing
        initialRoute: initialRoute,
        getPages: AppPages.routes,
        unknownRoute: AppPages.unknownRoute,
        defaultTransition: Transition.fade,
      ),
    );
  }
}
