import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// URLHelper class for handling URL parameters and query strings
/// Following Single Responsibility Principle by focusing only on URL management
class URLHelper {
  // Private constructor to prevent instantiation
  URLHelper._();

  /// Get URL parameter by name
  static String? getParameter(String paramName) {
    return Get.parameters[paramName];
  }

  /// Get all URL parameters
  static Map<String, String> getAllParameters() {
    return Map<String, String>.from(Get.parameters);
  }

  /// Build URL with parameters
  static String buildUrl(String route, {Map<String, String>? parameters}) {
    if (parameters == null || parameters.isEmpty) {
      return route;
    }

    final uri = Uri(path: route, queryParameters: parameters);

    return uri.toString();
  }

  /// Navigate to URL with parameters
  static void navigateWithParams(
    String route, {
    Map<String, String>? parameters,
    Map<String, String>? arguments,
  }) {
    final url = buildUrl(route, parameters: parameters);

    Get.toNamed(url, arguments: arguments);
  }

  /// Replace current URL with parameters
  static void replaceWithParams(
    String route, {
    Map<String, String>? parameters,
    Map<String, String>? arguments,
  }) {
    final url = buildUrl(route, parameters: parameters);

    Get.offNamed(url, arguments: arguments);
  }

  /// Update URL parameters without navigation
  static void updateUrlParams(Map<String, String> parameters) {
    // Simplified URL parameter update
    debugPrint('🔗 Updating URL params: $parameters');
  }

  /// Remove URL parameter
  static void removeUrlParam(String paramName) {
    // Simplified URL parameter removal
    debugPrint('🔗 Removing URL param: $paramName');
  }

  /// Get current route path without parameters
  static String getCurrentRoute() {
    return Get.currentRoute;
  }

  /// Check if current URL has specific parameter
  static bool hasParameter(String paramName) {
    return getParameter(paramName) != null;
  }

  /// Get parameter as int
  static int? getParameterAsInt(String paramName) {
    final value = getParameter(paramName);
    if (value != null) {
      return int.tryParse(value);
    }
    return null;
  }

  /// Get parameter as bool
  static bool? getParameterAsBool(String paramName) {
    final value = getParameter(paramName);
    if (value != null) {
      return value.toLowerCase() == 'true';
    }
    return null;
  }

  /// Build route with ID parameter
  static String buildRouteWithId(String baseRoute, String id) {
    return buildUrl(baseRoute, parameters: {'id': id});
  }

  /// Navigate to route with ID
  static void navigateToRouteWithId(String baseRoute, String id) {
    navigateWithParams(baseRoute, parameters: {'id': id});
  }

  /// Get ID from current URL
  static String? getCurrentId() {
    return getParameter('id');
  }

  /// Build pagination URL
  static String buildPaginationUrl(
    String baseRoute, {
    int? page,
    int? limit,
    String? search,
    String? sortBy,
    String? sortOrder,
  }) {
    final parameters = <String, String>{};

    if (page != null) parameters['page'] = page.toString();
    if (limit != null) parameters['limit'] = limit.toString();
    if (search != null && search.isNotEmpty) parameters['search'] = search;
    if (sortBy != null) parameters['sortBy'] = sortBy;
    if (sortOrder != null) parameters['sortOrder'] = sortOrder;

    return buildUrl(baseRoute, parameters: parameters);
  }

  /// Navigate with pagination parameters
  static void navigateWithPagination(
    String baseRoute, {
    int? page,
    int? limit,
    String? search,
    String? sortBy,
    String? sortOrder,
  }) {
    final url = buildPaginationUrl(
      baseRoute,
      page: page,
      limit: limit,
      search: search,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );

    Get.toNamed(url);
  }

  /// Get pagination parameters from URL
  static Map<String, dynamic> getPaginationParams() {
    return {
      'page': getParameterAsInt('page') ?? 1,
      'limit': getParameterAsInt('limit') ?? 10,
      'search': getParameter('search'),
      'sortBy': getParameter('sortBy'),
      'sortOrder': getParameter('sortOrder'),
    };
  }

  /// Update pagination parameters in URL
  static void updatePaginationParams({
    int? page,
    int? limit,
    String? search,
    String? sortBy,
    String? sortOrder,
  }) {
    final parameters = <String, String>{};

    if (page != null) parameters['page'] = page.toString();
    if (limit != null) parameters['limit'] = limit.toString();
    if (search != null) parameters['search'] = search;
    if (sortBy != null) parameters['sortBy'] = sortBy;
    if (sortOrder != null) parameters['sortOrder'] = sortOrder;

    updateUrlParams(parameters);
  }

  /// Clear all URL parameters
  static void clearAllParams() {
    // Simplified URL parameter clearing
    debugPrint('🔗 Clearing all URL params');
  }

  /// Get base URL without parameters
  static String getBaseUrl() {
    return Get.currentRoute;
  }

  /// Check if URL is valid
  static bool isValidUrl(String url) {
    try {
      Uri.parse(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Encode URL parameter
  static String encodeParam(String value) {
    return Uri.encodeComponent(value);
  }

  /// Decode URL parameter
  static String decodeParam(String value) {
    return Uri.decodeComponent(value);
  }
}
