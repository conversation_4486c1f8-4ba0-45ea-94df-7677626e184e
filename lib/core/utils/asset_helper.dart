import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// AssetHelper class for managing assets
/// This class provides methods to load images, icons, and other assets
class AssetHelper {
  // Base paths
  static const String _imagePath = 'assets/images/';
  static const String _iconPath = 'assets/icons/';
  static const String _lottiePath = 'assets/lottie/';

  // Image paths
  static String get logo => '${_imagePath}logo.png';
  static String get busIcon => '${_imagePath}bus.png';
  static String get marker => '${_imagePath}marker.png';
  static String get englishFlag => '${_imagePath}gb.png';
  static String get arabicFlag => '${_imagePath}ar.png';
  static String get disconnectInternet =>
      '${_imagePath}disconnect_internet.png';
  static String get done => '${_imagePath}done.png';
  static String get googleIcon => '${_imagePath}google_icon.png';
  static String get palestineFlag => '${_imagePath}palestine-flag-icon.png';

  // SVG paths
  static String get busHomeIcon => '${_imagePath}bus_home_icon.svg';
  static String get driverHomeIcon => '${_imagePath}driver_home_icon.svg';
  static String get supervisorHomeIcon =>
      '${_imagePath}supervisor_home_icon.svg';
  static String get locationIcon => '${_imagePath}location_icon.svg';
  static String get allDriverIcon => '${_imagePath}all_driver_icon.svg';
  static String get supervisorIcon => '${_imagePath}supervisor_icon.svg';
  static String get parentsIcon => '${_imagePath}parents_icon.svg';
  static String get groupsIcon => '${_imagePath}groups.svg';
  static String get backArrow => '${_imagePath}back-arrow.svg';
  static String get forwardArrow => '${_imagePath}forward-arrow.svg';
  static String get previousTripsIcon => '${_imagePath}previous_trips_icon.svg';

  // Lottie animations
  static String get loadingAnimation => '${_lottiePath}loading.json';
  static String get successAnimation => '${_lottiePath}success.json';
  static String get errorAnimation => '${_lottiePath}error.json';

  // Helper methods
  static Widget image(
    String path, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    Color? color,
  }) {
    return Image.asset(
      path,
      width: width,
      height: height,
      fit: fit,
      color: color,
    );
  }

  static Widget svg(
    String path, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    Color? color,
  }) {
    return SvgPicture.asset(
      path,
      width: width,
      height: height,
      fit: fit,
      colorFilter:
          color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
    );
  }

  // Common widgets
  static Widget get logoImage => image(logo, height: 64);
  static Widget get busIconImage => image(busIcon, height: 32);
  static Widget get markerImage => image(marker, height: 32);
  static Widget get englishFlagImage => image(englishFlag, height: 20);
  static Widget get arabicFlagImage => image(arabicFlag, height: 20);

  static Widget get busHomeIconSvg => svg(busHomeIcon, height: 20);
  static Widget get driverHomeIconSvg => svg(driverHomeIcon, height: 20);
  static Widget get supervisorHomeIconSvg =>
      svg(supervisorHomeIcon, height: 20);
  static Widget get locationIconSvg => svg(locationIcon, height: 20);
}
