import 'package:flutter/material.dart';

/// ResponsiveUtils class for handling responsive design
/// Following Single Responsibility Principle by focusing only on responsive design utilities
class ResponsiveUtils {
  // Private constructor to prevent instantiation
  ResponsiveUtils._();

  /// Screen breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Check if the current screen is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if the current screen is tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  /// Check if the current screen is desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  /// Get the current screen type
  static DeviceScreenType getDeviceType(BuildContext context) {
    if (isMobile(context)) {
      return DeviceScreenType.mobile;
    } else if (isTablet(context)) {
      return DeviceScreenType.tablet;
    } else {
      return DeviceScreenType.desktop;
    }
  }

  /// Get the current screen type (legacy)
  static ScreenType getScreenType(BuildContext context) {
    if (isMobile(context)) {
      return ScreenType.mobile;
    } else if (isTablet(context)) {
      return ScreenType.tablet;
    } else {
      return ScreenType.desktop;
    }
  }

  /// Get responsive value based on screen size
  static T getResponsiveValue<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final screenType = getScreenType(context);

    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? mobile;
      case ScreenType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    return getResponsiveValue<EdgeInsets>(
      context: context,
      mobile: const EdgeInsets.all(12),
      tablet: const EdgeInsets.all(18),
      desktop: const EdgeInsets.all(24),
    );
  }

  /// Get responsive horizontal padding based on screen size
  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context) {
    return getResponsiveValue<EdgeInsets>(
      context: context,
      mobile: const EdgeInsets.symmetric(horizontal: 12),
      tablet: const EdgeInsets.symmetric(horizontal: 36),
      desktop: const EdgeInsets.symmetric(horizontal: 72),
    );
  }

  /// Get responsive vertical padding based on screen size
  static EdgeInsets getResponsiveVerticalPadding(BuildContext context) {
    return getResponsiveValue<EdgeInsets>(
      context: context,
      mobile: const EdgeInsets.symmetric(vertical: 12),
      tablet: const EdgeInsets.symmetric(vertical: 18),
      desktop: const EdgeInsets.symmetric(vertical: 24),
    );
  }

  /// Get responsive width based on screen size
  static double getResponsiveWidth(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.width * percentage;
  }

  /// Get responsive height based on screen size
  static double getResponsiveHeight(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.height * percentage;
  }

  /// Get responsive font size based on screen size
  static double getResponsiveFontSize(BuildContext context, double size) {
    return getResponsiveValue<double>(
      context: context,
      mobile: size,
      tablet: size * 1.2,
      desktop: size * 1.4,
    );
  }

  /// Get font size (shorthand for getResponsiveFontSize)
  static double getFontSize(double size) {
    // For now, return the base size since we don't have context
    // In a real implementation, this would need context
    return size;
  }

  /// Get responsive font size based on device type with specific sizes
  static double getResponsiveFontSizeByDevice(
    BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
  }) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceScreenType.mobile:
        return mobile;
      case DeviceScreenType.tablet:
        return tablet;
      case DeviceScreenType.desktop:
        return desktop;
    }
  }

  /// Get responsive icon size based on device type
  static double getResponsiveIconSize(
    BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
  }) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceScreenType.mobile:
        return mobile;
      case DeviceScreenType.tablet:
        return tablet;
      case DeviceScreenType.desktop:
        return desktop;
    }
  }
}

/// Enum for screen types (legacy)
enum ScreenType { mobile, tablet, desktop }

/// Enum for device screen types
enum DeviceScreenType { mobile, tablet, desktop }

/// Extension on BuildContext for responsive utilities
extension ResponsiveContext on BuildContext {
  /// Check if the current screen is mobile
  bool get isMobile => ResponsiveUtils.isMobile(this);

  /// Check if the current screen is tablet
  bool get isTablet => ResponsiveUtils.isTablet(this);

  /// Check if the current screen is desktop
  bool get isDesktop => ResponsiveUtils.isDesktop(this);

  /// Get the current screen type
  ScreenType get screenType => ResponsiveUtils.getScreenType(this);

  /// Get responsive padding based on screen size
  EdgeInsets get responsivePadding =>
      ResponsiveUtils.getResponsivePadding(this);

  /// Get responsive horizontal padding based on screen size
  EdgeInsets get responsiveHorizontalPadding =>
      ResponsiveUtils.getResponsiveHorizontalPadding(this);

  /// Get responsive vertical padding based on screen size
  EdgeInsets get responsiveVerticalPadding =>
      ResponsiveUtils.getResponsiveVerticalPadding(this);

  /// Get responsive width based on screen size
  double responsiveWidth(double percentage) =>
      ResponsiveUtils.getResponsiveWidth(this, percentage);

  /// Get responsive height based on screen size
  double responsiveHeight(double percentage) =>
      ResponsiveUtils.getResponsiveHeight(this, percentage);

  /// Get responsive font size based on screen size
  double responsiveFontSize(double size) =>
      ResponsiveUtils.getResponsiveFontSize(this, size);
}
