import 'package:flutter/material.dart';

/// Color constants for the Busaty school bus management application
/// Following Single Responsibility Principle by focusing only on color definitions
class ColorConstants {
  // Private constructor to prevent instantiation
  ColorConstants._();

  // Primary Colors
  static const Color primary = Color(0xFF4A5CD0); // Main primary color
  static const Color secondary = Color(0xFF6C63FF); // Secondary color
  static const Color accent1 = Color(0xFF00C2CB); // Accent color 1
  static const Color accent2 = Color(0xFFFFA726); // Accent color 2

  // Legacy primary colors (for backward compatibility)
  static const Color mainColor = Color(0xff6f69cf); // Alternative primary color
  static const Color backgroundContainer = Color(
    0xff6059CB,
  ); // Background container color
  static const Color borderContainer = Color(
    0xff7771D2,
  ); // Border container color
  static const Color borderAvatar = Color(0xff8B86D8); // Border avatar color
  static const Color newRequest = Color(0xff4454C6); // New request color

  // Text Colors
  static const Color text = Color(0xff2C2828); // Main text color
  static const Color textPrimary = Color(0xff2C2828); // Primary text color
  static const Color textSecondary = Color(0xff808080); // Secondary text color
  static const Color titleSub = Color(0xff2C2828); // Title subtitle color
  static const Color textForm = Color(0xff808080); // Text form color
  static const Color textLogin = Color(0xff717171); // Login text color
  static const Color titleColor = Color(0xff707070); // Title color
  static const Color tabColors = Color(0xff707070); // Tab colors
  static const Color namePersonal = Color(0xff747474); // Personal name color
  static const Color dialogName = Color(0xff9FA1A8); // Dialog name color
  static const Color titleReasonColor = Color(0xffAEAFB5); // Title reason color
  static const Color subTitle = Color(0xffC8C8C8); // Subtitle color

  // Form and Input Colors
  static const Color underInput = Color(0xffA0A0A0); // Under input color
  static const Color grey5 = Color(0xffB7BBD2); // Grey 5 color
  static const Color fillFormField = Color(0xffF1F1F3); // Form field fill color
  static const Color fillFormFieldB = Color(
    0xffF5F5F5,
  ); // Form field fill color B
  static const Color iconInputColor = Color(0xff9AA0A2); // Input icon color
  static const Color inputRequestColor = Color(
    0xffDFDFDF,
  ); // Input request color

  // Status Colors
  static const Color greenSuccess = Color(0xff1FDE82); // Success green color
  static const Color acceptRequest = Color(0xff0DC400); // Accept request color
  static const Color redAccent = Color(0xffFF5564); // Red accent color
  static const Color darkRed = Color(0xffd95b65); // Dark red color
  static const Color error = Color(0xffFF0000); // Error color
  static const Color waitRequest = Color(0xffFFA200); // Wait request color
  static const Color refusalRequest = Color(
    0xffA6A2AD,
  ); // Refusal request color

  // Background colors
  static const Color background = Color(0xFFF5F7FA); // Main background color
  static const Color card = Colors.white; // Card background color
  static const Color surface = Colors.white; // Surface background color

  // Dark mode background colors
  static const Color backgroundDark = Color(0xFF121212); // Dark background
  static const Color cardDark = Color(0xFF1E1E1E); // Dark card background
  static const Color surfaceDark = Color(0xFF2D2D2D); // Dark surface background
  static const Color sidebarDark = Color(0xFF1A1A1A); // Dark sidebar background
  static const Color headerDark = Color(0xFF252525); // Dark header background

  // Basic Colors
  static const Color white = Color(0xffffffff); // White color
  static const Color black = Color(0xff000000); // Black color
  static const Color transparent = Colors.transparent; // Transparent color

  // Text colors for dark mode
  static const Color textPrimaryDark = Color(
    0xFFE0E0E0,
  ); // Primary text in dark mode
  static const Color textSecondaryDark = Color(
    0xFFB0B0B0,
  ); // Secondary text in dark mode
  static const Color textTertiaryDark = Color(
    0xFF808080,
  ); // Tertiary text in dark mode

  // Aliases for backward compatibility
  static const Color primaryColor = primary; // Alias for primary
  static const Color primaryLightColor = mainColor; // Alias for mainColor
  static const Color primaryDarkColor =
      backgroundContainer; // Alias for backgroundContainer

  static const Color secondaryColor = black; // Alias for black
  static const Color secondaryLightColor = text; // Alias for text
  static const Color secondaryDarkColor = black; // Alias for black

  static const Color accentColor = redAccent; // Alias for redAccent
  static const Color highlightColor = mainColor; // Alias for mainColor
  static const Color complementaryColor = primary; // Alias for primary

  // Additional colors for backward compatibility
  static const Color tertiaryColor = fillFormFieldB; // Light background color
  static const Color tertiaryLightColor = white; // White color
  static const Color tertiaryDarkColor = grey5; // Grey color
  static const Color neutralGreyColor = textForm; // Medium grey color

  static const Color warning = waitRequest; // Warning color
  static const Color successColor = greenSuccess; // Alias for greenSuccess
  static const Color warningColor = waitRequest; // Alias for waitRequest
  static const Color errorColor = error; // Alias for error
  static const Color infoColor = primary; // Alias for primary
  static const Color alertColor = redAccent; // Alias for redAccent

  static const Color backgroundLightColor = white; // Alias for white
  static const Color backgroundDarkColor = backgroundDark; // Dark background

  static const Color surfaceLightColor =
      fillFormField; // Alias for fillFormField
  static const Color surfaceDarkColor = cardDark; // Dark card background

  static const Color textPrimaryLightColor = text; // Alias for text
  static const Color textSecondaryLightColor = textForm; // Alias for textForm
  static const Color textHeadingLightColor = titleSub; // Alias for titleSub
  static const Color textPrimaryDarkColor =
      textPrimaryDark; // Primary text in dark mode
  static const Color textSecondaryDarkColor =
      textSecondaryDark; // Secondary text in dark mode
  static const Color textHeadingDarkColor =
      textPrimaryDark; // Heading text in dark mode

  // Gradient Colors
  static const List<Color> primaryGradient = [primaryColor, primaryLightColor];

  static const List<Color> secondaryGradient = [
    secondaryColor,
    secondaryLightColor,
  ];

  static const List<Color> tertiaryGradient = [
    tertiaryColor,
    tertiaryLightColor,
  ];

  // Background Gradient for Light Mode
  static const List<Color> backgroundGradientLight = [
    tertiaryLightColor, // White
    tertiaryColor, // Light background
  ];

  // Background Gradient for Dark Mode
  static const List<Color> backgroundGradientDark = [
    secondaryDarkColor, // Black
    secondaryLightColor, // Light Black
  ];

  // Accent Gradients
  static const List<Color> accentGradient = [
    accentColor, // Red Accent
    mainColor, // Main Color
  ];

  static const List<Color> highlightGradient = [
    mainColor, // Main Color
    primary, // Primary Color
  ];

  // Status Colors Map - For easy access in different contexts
  static const Map<String, Color> statusColors = {
    'success': successColor,
    'warning': warningColor,
    'error': errorColor,
    'info': infoColor,
    'alert': alertColor,
    'accept': acceptRequest,
    'wait': waitRequest,
    'refuse': refusalRequest,
    'new': newRequest,
  };

  // Semantic colors
  static final Color success = greenSuccess;
  static final Color info = primary.withAlpha(200);

  // Additional colors for accessibility components
  static const Color disabled = Color(0xFFE0E0E0);
  static const Color textDisabled = Color(0xFF9E9E9E);
  static const Color border = Color(0xFFE0E0E0);
}
